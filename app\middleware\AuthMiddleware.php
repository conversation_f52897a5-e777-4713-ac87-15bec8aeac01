<?php
namespace app\middleware;

use app\model\User;
use ReflectionClass;
use Webman\Exception\BusinessException;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class AuthMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler) : Response
    {
        // 通过反射获取控制器哪些方法不需要登录
        $controller = new ReflectionClass($request->controller);
        $noNeedLogin = $controller->getDefaultProperties()['noNeedLogin'] ?? [];

        // 访问的方法需要登录
        if (!in_array($request->action, $noNeedLogin)) {
            $token = $request->header('Authorization', $request->input('authorization', ''));
            if (!$token) {
                throw new BusinessException('请先登录', 401);
            }
            User::loginByToken($token); // 登录
        }

        // 不需要登录，请求继续向洋葱芯穿越
        return $handler($request);
    }
}