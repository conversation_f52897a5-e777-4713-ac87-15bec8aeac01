<?php

namespace app\process;

use app\api\service\AiTaskService;
use app\model\ShortLink;
use app\model\TodoItems;
use support\Db;
use support\Log;
use Workerman\Crontab\Crontab;

class ScheduleTask1
{
    public function onWorkerStart()
    {
        // 待办提醒, 每分钟执行一次
        new Crontab('0 */1 * * * *', function () {
            $list = TodoItems::getNotifyList();
            $nextNotifyMap = [];
            foreach ($list as $item) {
                $userId = $item['user_id'];
                unset($item['user_id']);
                $finishText = '任务完成后将待办标记为已完成';
                if (!empty($item['notify_cycle'])) {
                    $nextNotifyMap[$item['id']] = TodoItems::getNextCycleNotifyTime($item['notify_time'], $item['notify_cycle']);
                    $finishText = '任务完成后不要将待办标记为已完成';
                }
                if ($item['type'] == TodoItems::TYPE_AI) {
                    Log::info("开始执行AI待办任务(id=$item[id])");
                    $item['link'] = ShortLink::genShortUrl($userId, TodoItems::PAGE_DETAIL_ROUTE . $item['id']);
                    AiTaskService::createTask($userId, "你是AI销售助手Ivy。请根据以下待办信息，自动完成任务并输出结果。（{$finishText}）

要求：
- 充分思考并利用你的现有能力来完成待办目标
- 最终输出的结果要尽可能详细，并附上链接
- 语言风格自然、分段、简明，适当使用表情符号（1-2个），纯中文
- 不暴露任何ID信息
- 保留每条关键信息的原始链接，用Markdown无序列表格式输出 [待办/客户/商机/文件标题](链接地址)

待办信息如下：\n\n" . json_encode($item, JSON_UNESCAPED_UNICODE), 'todo-task-' . $item['id'], $item['id']);
                } else {
                    TodoItems::pushTodoNotice($userId, $item['id'], $item['title']);
                    Log::info("已生成待办提醒(id=$item[id]})");
                }
            }
            if (!empty($nextNotifyMap)) {
                TodoItems::whereIn('id', array_keys($nextNotifyMap))->update(['notify_time' => Db::raw(TodoItems::buildCaseSql('id', $nextNotifyMap))]);
            }
        });

    }
}