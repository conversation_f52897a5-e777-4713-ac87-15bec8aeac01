<?php

namespace app\wechat\controller;

use app\controller\BaseController;
use app\model\ShortLink;
use app\model\UserChat;
use app\model\UserWx;
use app\pyapi\service\LinkService;
use app\wechat\service\UserService;
use app\wxwork\service\WxUserService;
use library\redisKeys;
use library\Sms;
use library\tool;
use library\Voice;
use Respect\Validation\Validator as v;
use support\Redis;
use Webman\RedisQueue\Redis as RedisQueue;
use GuzzleHttp\Client;
use support\Log;
use support\Request;

class HomeController extends BaseController
{
    protected $noNeedLogin = [];

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendText(Request $request)
    {
        $data = v::input($request->post(), [
            'user_wx_id' => v::notEmpty()->setName('user_wx_id'),
            'user_name' => v::notEmpty()->setName('user_name'),
            'token' => v::notEmpty()->setName('token'),
            'content' => v::notEmpty()->setName('内容'),
            'app_id' => v::notEmpty()->setName('app_id'),
        ]);
        $contentType = $request->post('content_type');
        $content = trim($data['content']);
        //是否是语音消息
        if ($contentType == 'audio') {
            $transRes = Voice::voiceToText($content, 'silk');
            if (empty($transRes)) {
                return $this->success(['content' => "不好意思，没有听清您再说什么~"]);
            }
            $content = $transRes;
            Log::info('语音消息：' . $content);
        }
        //是否绑定手机号
        $info = UserService::regis($data['app_id'], $data['user_wx_id']);
        if (empty($info['user_id'])) {
            //处理手机号消息
            if (is_numeric($content) && strlen($content) == 11) {
                //绑定手机号
                $resp = "好的，已向 " . $content . " 发送6位验证码，请查收并输入。";
                $key = redisKeys::USER_MOBILE_CODE . $content;
                $code = rand(100000, 999999);
                //发送验证码
                Sms::sendSms($content, $code);
                Redis::setEx($key, 600, $code);

                UserWx::editByThirdId($data['app_id'], $data['user_wx_id'], ['mobile' => $content, 'status' => UserWx::STATUS_SEND]);
                sleep(rand(3, 4));
                return $this->success(['content' => $resp]);
            }

            //验证手机号验证码
            if (!empty($info['status']) && $info['status'] == UserWx::STATUS_SEND) {
                $code = $content;
                if (!is_numeric($code) || strlen($code) != 6) {
                    sleep(rand(3, 4));
                    return $this->success(['content' => '验证码错误']);
                }
                $cacheCode = Redis::get(redisKeys::USER_MOBILE_CODE . $info['mobile']);
                if (empty($cacheCode) || $cacheCode != $code) {
                    sleep(rand(3, 4));
                    return $this->success(['content' => '验证码错误']);
                } else {
                    $msg = "验证成功！🎉 欢迎加入！";
                    UserWx::editByThirdId($data['app_id'], $data['user_wx_id'], ['status' => UserWx::STATUS_SUCCESS]);
                    UserService::doRegis($info);
                    sleep(rand(3, 4));
                    return $this->success(['content' => $msg]);
                }
            }
            //回复消息
            $msg = "你好！我是你的AI销售助理Ivy 

可以帮你智能管理客户、商机跟进、日程提醒，还有话术建议和数据分析功能哦

请输入手机号，我会发验证码给你。
";
            sleep(rand(3, 4));
            return $this->success(['content' => $msg]);
        }
        $userId = $info['user_id'];
        $taskId = tool::uuid();
        $sessionId = WxUserService::getSessionId($userId);
        UserChat::addOne([
            'user_id' => $userId,
            'content' => $content,
            'is_robot' => 0,
        ]);
        //发送AI请求
        try {
            $client = new Client();
            $response = $client->request('POST', 'http://' . env('AI_AGENT_SERVER') . '/task', [
                'json' => [
                    'query' => $content,
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'task_id' => $taskId
                ]
            ]);
            Log::info('向ai发送请求，返回结果：' . var_export($response->getBody(), true));
        } catch (\Exception $e) {
            Log::error('向ai服务发送请求失败：' . $e->getMessage());
            sleep(3);
            return $this->success(['content' => '网络异常，请稍后重试']);
        }
        //异步消息队里处理结果
        $res = RedisQueue::send('ai-task', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'user_wxid' => $data['user_wx_id'],
                'query' => $content,
            ]
        );
        if ($res !== true) {
            Log::error('ai-task 投递失败');
            sleep(3);
            return $this->success(['content' => '网络异常，请稍后重试']);
        }
        // @todo 生成映射token
        sleep(rand(4, 6));
        $link = LinkService::genShortLink($userId, LinkService::genTaskResult($taskId));
        $msg = "⏳ 正在处理中，查看详情↓↓
{$link}";
        return $this->success(['content' => '']);
    }

    public function saveRes(Request $request)
    {
        $data = v::input($request->post(), [
            'content' => v::notEmpty()->setName('内容'),
        ]);
        $userId = 1;
        UserChat::addOne([
            'user_id' => $userId,
            'content' => $data['content'],
            'is_robot' => 1,
        ]);
        return json(['code' => 0, 'msg' => 'ok']);
    }


}