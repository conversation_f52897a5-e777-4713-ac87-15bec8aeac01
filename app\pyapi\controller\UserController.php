<?php

namespace app\pyapi\controller;

use app\controller\BaseController;
use app\model\User;
use app\pyapi\service\LinkService;
use app\wxwork\service\NoticeService;
use Respect\Validation\Validator as v;
use support\Log;
use support\Request;

class UserController extends BaseController
{

    public function profile(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
            'name' => v::notEmpty()->setName('用户名'),
            'business_type' => v::notEmpty()->setName('行业'),
            'profile' => v::notEmpty()->setName('用户描述'),
        ]);
        $data['company'] = $request->post('company', '');
        User::edit($data['user_id'], [
            'name' => $data['name'],
            'business_type' => $data['business_type'],
            'company' => $data['company'],
            'profile' => $data['profile'],
        ]);
        Log::info('用户信息保存成功' . var_export($data, true));
        //推送一条消息
        $msg1 = "我是Ivy，您的智能销售助手。我的使命是帮您高效管理客户、商机和待办事项，让销售工作更轻松高效。

我可以为您：
• 整理客户信息 📋
• 跟进业务商机 🤝
• 智能管理待办事项 ✅
• 提供个性化建议 💡";
        $msg2 = "需要帮您建立客户库吗？📁

只需发我名片、微信对话或客户资料，我会自动录入系统，随时可查可改 👇
（示例输入：陈敏 拓维信息 销售总监 ***********）";
        NoticeService::pushNotice($data['user_id'], $msg1, 7, payload: ['url' => LinkService::genChat()]);
        NoticeService::pushNotice($data['user_id'], $msg2, 10, payload: ['url' => LinkService::genChat()]);
        return $this->success('保存成功');
    }

    public function editProfile(Request $request)
    {
        $post = $request->post();
        v::input($request->post(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $data = [];
        if (!empty($post['name'])) {
            $data['name'] = $post['name'];
        }
        if (!empty($post['business_type'])) {
            $data['business_type'] = $post['business_type'];
        }
        if (!empty($post['company'])) {
            $data['company'] = $post['company'];
        }
        if (!empty($post['profile'])) {
            $data['profile'] = $post['profile'];
        }
        if (!empty($data)) {
            User::edit($post['user_id'], $data);
            return $this->success('保存成功');
        } else {
            return $this->success('没有需要编辑保存的内容');
        }
    }

}