<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;

class UserPpt extends BaseModel
{
    use SoftDeletes;

    protected $table = 'user_ppt';
    protected $primaryKey = 'id';
    public $timestamps = true;

    public static function getOneByPptId($userId, $pptId, $fields = ['*'])
    {
        return self::getUserQuery($userId, $fields)
            ->where('ppt_id', $pptId)
            ->first();
    }

}