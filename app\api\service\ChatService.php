<?php

namespace app\api\service;

use app\model\Attachment;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use app\model\User;
use app\model\UserChat;
use library\ai\deepseek;
use library\ai\openai;
use library\ai\qwen;
use library\tool;
use support\Db;
use support\Log;
use Webman\Exception\BusinessException;

class ChatService
{
    /**
     * 归类昨天的用户消息到不同客户会话下
     */
    public static function classifyUserChat($userId)
    {
        // 获取昨天的聊天记录
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $chats = UserChat::getUserQuery($userId, ['id', 'content', 'is_robot'])->whereBetween('created_at', [$yesterday . ' 00:00:00', $yesterday . ' 23:59:59'])->whereNot('content_type', 'operation')->get()->toArray();
        // AI消息只获取最终结果输出(文字过长时中间部分省略)
        foreach ($chats as &$chat) {
            if ($chat['is_robot']) {
                $content = json_decode($chat['content'], true);
                $chat['content'] = $content[count($content) - 1]['content'] ?? '';
            }
            if (mb_strlen($chat['content']) > 300) {
                $chat['content'] = mb_substr($chat['content'], 0, 150) . '...' . mb_substr($chat['content'], -150);
            }
        }
        if (empty($chats)) {
            return;
        }
        // 获取客户列表
        $customers = Customers::getUserQuery($userId, ['customer_id', 'company_name', 'company_short_name', 'contact_name', 'contact_title', 'phone'])->get()->toArray();
        if (empty($customers)) {
            return;
        }
        // 获取客户下的商机列表
        $customerIds = array_column($customers, 'customer_id');
        $businesses = BusinessOpportunities::getUserQuery($userId, ['bo_id', 'customer_id', 'title'])->whereIn('customer_id', $customerIds)->get()->toArray();
        $customerMapBusinesses = array_fill_keys($customerIds, []);
        $businessIds = [];
        $boIdMapCustomerId = [];
        foreach ($businesses as $business) {
            $cid = $business['customer_id'];
            unset($business['customer_id']);
            $businessIds[] = $business['bo_id'];
            $customerMapBusinesses[$cid][] = $business;
            $boIdMapCustomerId[$business['bo_id']] = $cid;
        }
        // 获取客户下的待办事项列表
        $todoList = TodoItems::getUserQuery($userId, ['id', 'customer_id', 'title'])->whereIn('customer_id', $customerIds)->get()->toArray();
        $customerMapTodos = array_fill_keys($customerIds, []);
        foreach ($todoList as $todo) {
            $cid = $todo['customer_id'];
            unset($todo['customer_id']);
            $customerMapTodos[$cid][] = $todo;
        }
        // 获取客户下的附件列表
        $fields = ['id', 'parent_id', 'name', 'type', 'classify', 'classify_id'];
        $list = Attachment::getUserQuery($userId, $fields)->whereIn('classify', [Attachment::CLASSIFY_CUSTOMER, Attachment::CLASSIFY_BUSINESS])->where('classify_id', '>', 0)->get()->toArray();
        $customerMapAttachments = array_fill_keys($customerIds, []);
        foreach ($list as $item) {
            $classify = $item['classify'];
            $classifyId = $item['classify_id'];
            unset($item['classify'], $item['classify_id']);
            if ($classify == Attachment::CLASSIFY_CUSTOMER) {
                $customerMapAttachments[$classifyId][] = $item;
            } elseif ($classify == Attachment::CLASSIFY_BUSINESS) {
                $cid = $boIdMapCustomerId[$classifyId] ?? 0;
                $cid && $customerMapAttachments[$cid][] = $item;
            }
        }
        // 构建客户列表关联信息
        foreach ($customers as &$customer) {
            $cid = $customer['customer_id'];
            $customer['businesses'] = $customerMapBusinesses[$cid] ?? [];
            $customer['todos'] = $customerMapTodos[$cid] ?? [];
            $customer['file_tree'] = tool::buildTree($customerMapAttachments[$cid] ?? []);
        }
        $userInfo = json_encode(User::getById($userId, ['name', 'mobile', 'company', 'profile', 'business_type']), JSON_UNESCAPED_UNICODE);
        // 请求AI进行分类
        $prompt = <<<EOF
## 角色与任务
你是一位专业的销售行业聊天内容分类专家，专注于精准匹配客户与聊天记录。

## 输入数据
- 客户信息列表（包含客户ID及详细信息）
- 聊天消息列表（每条消息包含消息ID、is_robot标志、内容等）

## 处理要求
1. 严格匹配标准：
   - 仅返回有明确关联的客户-消息组合
   - 不强行匹配不确定的关联
   - 无匹配时返回空数组[]

2. 消息匹配规则：
   - **必须成对包含用户消息(is_robot=0)和AI回复(is_robot=1)**
   - 包含与客户直接相关的上下文消息
   - **通过对话上下文判断用户的意图是否与客户相关，仅匹配用户意图与客户相关联的完整对话**
   - 匹配依据包括但不限于：
     * 客户基本信息
     * 商机信息
     * 待办事项
     * 关联文件
     * 其他显式关联内容

3. 消息不匹配规则(包括但不限定于)：
   - 与客户无关的消息
   - 无法唯一确定一个客户的消息
   - 闲聊或其他无意义文字
   - 查询或处理与自己或销售物料相关的事项
   - 查询或处理没有特定对象的多个或所有对象(如查询/处理/整理所有xx)

4. 上下文处理：
   - 识别并包含相关话题的完整对话流
   - 确保对话上下文语义连贯性

## 输出格式
```json
{
    "result": [
        {
            "customer_id": 数字,
            "chat_ids": [数字数组]
        }
    ]
}
```

## 示例
输入：客户信息+聊天记录
输出：
```json
{"result": [{"customer_id": 1, "chat_ids": [1, 2, 3, 4]}, {"customer_id": 2, "chat_ids": [5, 6, 7, 8]}]}
```

无匹配时输出：
```json
{}
```
EOF;
        $messages = [
            ['role' => 'system', 'content' => $prompt],
            ['role' => 'user', 'content' => "下面是我的所有客户信息和聊天消息列表，请根据这些信息按照要求帮我完成分类并返回json结果\n\n## 我的个人信息\n$userInfo\n\n## 客户列表\n" . json_encode($customers, JSON_UNESCAPED_UNICODE) . "\n\n## 聊天消息列表\n" . json_encode($chats, JSON_UNESCAPED_UNICODE)],
        ];
        $result = deepseek::send($messages, deepseek::MODEL_V3, 0.5, 'json');
        // 使用json_schema模式请求
        // $result = openai::send($messages, openai::MODEL_GPT41_MINI, 0.5, ['name' => 'result', 'schema' => [
        //     'type' => 'object',
        //     'properties' => [
        //         'result' => [
        //             'type' => 'array',
        //             'items' => [
        //                 'type' => 'object',
        //                 'properties' => [
        //                     'customer_id' => [
        //                         'type' => 'integer',
        //                     ],
        //                     'chat_ids' => [
        //                         'type' => 'array',
        //                         'items' => [
        //                             'type' => 'integer',
        //                         ],
        //                     ],
        //                 ],
        //             ],
        //         ],
        //     ],
        // ]]);
        $message = $result['choices'][0]['message']['content'] ?? '';
        $data = json_decode($message, true);
        if (!$data || !isset($data['result']) || !is_array($data['result'])) {
            return;
        }
        foreach ($data['result'] as $item) {
            $cid = $item['customer_id'] ?? 0;
            $chatIds = $item['chat_ids'] ?? [];
            if (!$cid || empty($chatIds) || !is_array($chatIds)) {
                continue;
            }
            UserChat::whereIn('id', $chatIds)->update(['session_id' => "customer-$cid"]);
        }
        
    }
}