<?php

namespace app\model;

use support\Model;

class MainAgentSessions extends Model
{
    protected $table = 'main_agent_sessions';
    protected $primaryKey = 'id';
    public $timestamps = true;
    /**
     * 时间戳存储格式
     *
     * @var string
     */
    protected $dateFormat = 'U';

    public static function getLastOne($userId, $fields = ['*'])
    {
        return self::select($fields)
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    public static function delByUserId($userId)
    {
        return self::where('user_id', $userId)->delete();
    }

    public static function delBySessionId($userId, $sessionId)
    {
        return self::where('user_id', $userId)->where('session_id', $sessionId)->delete();
    }



    /**
     * 根据AI消息的内容和时间戳从memory中删除对应消息完整的对话轮次（包括用户消息、工具调用、思考步骤等）
     * 
     * @param string $sessionId 会话ID
     * @param string $content AI回复消息内容
     * @param int $createdAt 创建时间戳
     * @return bool
     */
    public static function removeMessageFromMemoryByContent($sessionId, $content, $createdAt)
    {
        $session = self::where('session_id', $sessionId)->first();
        if (empty($session) || empty($session->memory)) {
            return true;
        }

        $memory = json_decode($session->memory, true);
        if (empty($memory['runs'])) {
            return true;
        }

        $delIndex = false;
        $targetContent = $content;

        // 遍历runs数组，删除匹配的消息对话
        foreach ($memory['runs'] as $i => &$run) {
            // 匹配内容和时间戳（允许一定的时间误差）
            $contentMatch = ($run['content'] ?? '') === $targetContent;
            $timeMatch = abs(($run['created_at'] ?? 0) - $createdAt) <= 300; // 允许300秒误差

            // 如果条件都匹配，则删除该轮对话
            if ($contentMatch && $timeMatch) {
                $delIndex = $i;
                break;
            }
        }

        // 如果有修改，更新数据库
        if ($delIndex !== false) {
            unset($memory['runs'][$delIndex]); // 删除该轮对话
            $memory['runs'] = array_values($memory['runs']); // 重新排序
            return self::where('session_id', $sessionId)
                ->update(['memory' => json_encode($memory, JSON_UNESCAPED_UNICODE)]);
        }

        return true;
    }

}