<?php

namespace app\queue\redis\fast;

use app\api\service\AiTaskService;
use library\redisKeys;
use support\Log;
use support\Redis;
use Webman\Exception\BusinessException;
use Webman\RedisQueue\Consumer;
use Webman\RedisQueue\Redis as RedisQueue;

/**
 * 处理前端H5投递的ai任务结果
 */
class AppAiTaskQueue implements Consumer
{
    // 要消费的队列名
    const QUEUE_NAME = 'ai-task-app';
    public $queue = self::QUEUE_NAME;

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';

    /**
     * 消费
     * data:  user_id, task_id, content, query
     **/
    public function consume($data)
    {
        //Log::info("开始消费{$this->queue}队列: ". var_export($data, true));
        if (empty($data['task_id']) || empty($data['user_id'])) {
            throw new BusinessException('参数错误');
        }
        $taskId = $data['task_id'];
        $userId = $data['user_id'];
        $todoId = $data['todo_id'] ?? 0;
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        // 是否完成
        $res = Redis::lIndex($key, -1);
        if ($res) {
            $res = json_decode($res, true);
            if (AiTaskService::isFinished($res)) {
                return AiTaskService::finished($userId, $data['session_id'] ?? '', $taskId, $todoId);
            }
        }
        // 是否超时
        if (!empty($data['task_expire_time']) && $data['task_expire_time'] < time()) {
            Log::error($this->queue . '队列，执行超时 data=' . var_export($data, true));
            Redis::rPush($key, json_encode(['event' => 'RunCompleted', 'content' => '执行超时'], JSON_UNESCAPED_UNICODE));
            return AiTaskService::finished($userId, $data['session_id'] ?? '', $taskId, $todoId);
        }
        // 未完成, 投递回去下次处理
        RedisQueue::send($this->queue, $data, 5);
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        // 无需反序列化
        Log::error('ai-task队列消费失败，data=【' . var_export($package, true) . '】 error=【' . $e->getMessage() . '】');
    }
}