<?php

namespace app\api\service;


class FeatGuidanceService
{

    public static function getGuidance()
    {
        return [
            [
                "id" => 1,
                "title" => '客户管理',
                'child' => [
                    [
                        'id' => 1,
                        'label' => '新建客户',
                        'word' => '新建客户：',
                        'eg' => '示例：腾讯，联系人张三，电话13988886666',
                    ],
                    [
                        'id' => 2,
                        'label' => '查询客户',
                        'word' => '查询客户：',
                        'eg' => '示例：张三的联系方式发我下',
                    ], [
                        'id' => 3,
                        'label' => '修改客户信息',
                        'word' => '修改客户信息：',
                        'eg' => '示例：张三职位是CTO，生日10月10号，比较重视技术细节',
                    ],
                ]
            ],
            [
                "id" => 2,
                "title" => '商机管理',
                'child' => [
                    [
                        'id' => 4,
                        'label' => '新建商机',
                        'word' => '新建商机：',
                        'eg' => '示例：腾讯有AI教育采购需求，预算100万元',
                    ],
                    [
                        'id' => 5,
                        'label' => '查询商机',
                        'word' => '查询商机：',
                        'eg' => '示例：我有哪些商机在商务谈判阶段？',
                    ], [
                        'id' => 6,
                        'label' => '添加商机跟进',
                        'word' => '添加商机跟进：',
                        'eg' => '示例：记录一条跟进，刚见了张三他们可能会有一些新需求',
                    ],
                ]
            ],
            [
                "id" => 3,
                "title" => '待办管理',
                'child' => [
                    [
                        'id' => 7,
                        'label' => '新建待办',
                        'word' => '新建待办：',
                        'eg' => '示例：明天上午10点要跟张三开会',
                    ],
                    [
                        'id' => 8,
                        'label' => '查询待办',
                        'word' => '查询待办：',
                        'eg' => '示例：我今天要做哪些事？',
                    ], [
                        'id' => 9,
                        'label' => '定时AI任务',
                        'word' => '定时AI任务：',
                        'eg' => '示例：每周五根据本周工作，生成总结',
                    ],
                ]
            ],
            [
                "id" => 4,
                "title" => '文件管理',
                'child' => [
                    [
                        'id' => 10,
                        'label' => '写文件',
                        'word' => '写文件：',
                        'eg' => '示例：帮我写一个AI教育项目的报价单',
                    ],
                    [
                        'id' => 11,
                        'label' => '找文件',
                        'word' => '找文件：',
                        'eg' => '示例：最新的产品介绍发我一份',
                    ], [
                        'id' => 12,
                        'label' => '存文件',
                        'word' => '存文件：',
                        'eg' => '示例：上传[服务建议书.pdf]，帮我存一下',
                    ],
                    [
                        'id' => 13,
                        'label' => '知识问答',
                        'word' => '知识库查询：',
                        'eg' => '示例：AI教育项目的合同条款发我下',
                    ],
                ]
            ],
            [
                "id" => 5,
                "title" => '信息搜索',
                'child' => [
                    [
                        'id' => 14,
                        'label' => '公司信息',
                        'word' => '搜索公司信息：',
                        'eg' => '示例：查腾讯的工商信息',
                    ],
                    [
                        'id' => 15,
                        'label' => '行业新闻',
                        'word' => '搜索行业新闻：',
                        'eg' => '示例：每周一发我AI教育领域上周的新闻',
                    ],
                ]
            ],
            [
                "id" => 6,
                "title" => '销售建议',
                'child' => [
                    [
                        'id' => 16,
                        'label' => '沟通话术',
                        'word' => '生成沟通话术：',
                        'eg' => '示例：AI教育项目张三觉得我们报价高，有没有合适的应对的话术',
                    ],
                    [
                        'id' => 17,
                        'label' => '商机推进',
                        'word' => '生成沟通话术：',
                        'eg' => '示例：AI教育项目卡住了，下一步怎么推进？',
                    ],
                ]
            ],
        ];
    }

}