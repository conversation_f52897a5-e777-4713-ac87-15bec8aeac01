<?php

namespace app\controller;

use app\model\BaseModel;
use support\Request;
use Respect\Validation\Validator as v;
use support\Db;
use think\Validate;
use Workerman\Connection\TcpConnection;
use Workerman\Protocols\Http\ServerSentEvents;
use Workerman\Timer;

class BaseController
{
    /**
     * 控制器对应的模型
     */
    protected BaseModel $model;
    /**
     * 接口验证规则(键为对应方法名，值为验证规则，save分为add和edit)
     */
    protected array $validateRules = [];
    /**
     * 列表接口查询字段
     */
    protected $indexFields = ['*'];
    /**
     * 列表支持的前端排序字段, 如填写['id'], 则前端可传['orderby' => ['id' => 'desc或asc']]
     */
    protected $indexSortFields = [];
    /**
     * 列表接口要使用的过滤条件, 每一项的键为字段名，值为查询条件(对应where方法的operator字段)，如：['name' => 'like']
     */
    protected $indexFilterFields = [];
    /**
     * 列表接口默认使用的排序, 每一项的键为字段名，值为asc或desc，如：['id' => 'asc']
     */
    protected $indexSort = [];
    /**
     * thinkphp验证器
     */
    protected Validate $validate;

    public function __construct()
    {
        
    }

    protected function success($data = null, int $code = 0, string $msg = "ok"): \support\Response
    {
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

    protected function error(string $msg = 'fail', int $code = 1, $data = null): \support\Response
    {
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

    /**
     * 流式响应
     *
     * @param Request $request
     * @param callable $chunkCallback 返回true则结束, 参数: &$data为要发送的数据
     * @param integer $interval 发送间隔，单位秒
     */
    protected function stream(Request $request, callable $chunkCallback, float $interval = 1)
    {
        //获取连接
        $connection = $request->connection;
        // 定时发送http包体
        // 首先发送一个 Content-Type: text/event-stream 头的响应
        // $connection->send(new \support\Response(200, ['Content-Type' => 'text/event-stream'], "\r\n")); // 不会走响应中间件
        // 定时向客户端推送数据
        // $connection->send(new ServerSentEvents(['event' => 'message', 'data' => '[START]']));
        $lastSendTime = 0;
        $timer_id = Timer::add($interval, function () use ($connection, &$timer_id, $chunkCallback, &$lastSendTime) {
            if ($lastSendTime == 0) { // 第一个send用于发送请求头, 内容不会被接收, 没有用send发送响应请求头的时候需要发送一条初始化消息
                $connection->send(new ServerSentEvents(['event' =>'message', 'data' => '[START]']));
                $lastSendTime = time();
            }
            $end = false;
            $data = null;
            $end = $chunkCallback($data);
            // 连接关闭的时候要将定时器删除，避免定时器不断累积导致内存泄漏
            if ($connection->getStatus() !== TcpConnection::STATUS_ESTABLISHED) {
                Timer::del($timer_id);
                return;
            }
            // 发送message事件，事件携带的数据为data，消息id可以不传
            if ($data) {
                $connection->send(new ServerSentEvents(['event' => 'message', 'data' => is_string($data) ? $data : json_encode($data, JSON_UNESCAPED_UNICODE)]));
                $lastSendTime = time();
            } else {
                if (time() - $lastSendTime > 30) {
                    $connection->send(new ServerSentEvents(['event' => 'message', 'data' => '[PING]']));
                    $lastSendTime = time();
                }
            }
            if ($end) {
                // 删除不用的定时器，避免定时器越来越多内存泄漏
                Timer::del($timer_id);
                $connection->send(new ServerSentEvents(['event' => 'message', 'data' => '[DONE]']));
                $connection->close();
            }
        });
        return response()->withHeaders(['Content-Type' => 'text/event-stream']);
    }

    protected function paginate(\Illuminate\Pagination\LengthAwarePaginator $result): \support\Response
    {
        return $this->success(data:$this->getPaginateData($result));
    }

    protected function getPaginateData(\Illuminate\Pagination\LengthAwarePaginator $result): array
    {
        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $result->currentPage(),
            'limit' => $result->perPage(),
        ];
    }

    /**
     * 通用保存接口
     */
    public function save(Request $request)
    {
        $data = $request->post();
        $userId = $data['user_id'] = getUserId();
        $id = $data['id'] ?? 0;
        if ($id) {
            !empty($this->validateRules['edit']) && v::input($data, $this->validateRules['edit']);
            !empty($this->validate) && $this->validate->scene('edit')->check($data);
            $model = $this->model::query()->where('user_id', $userId)->find($id);
            if (empty($model)) return $this->error('更新失败，记录不存在');
        } else {
            $model = (new $this->model);
            !empty($this->validateRules['add']) && v::input($data, $this->validateRules['add']);
            !empty($this->validate) && $this->validate->scene('add')->check($data);
        }
        $this->saveOpt($request, $model, $data);
        return $this->success(['id' => $model->getKey()]);
    }

    /**
     * 通用保存接口自定义保存操作
     */
    protected function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        return $model->fill($data)->save();
    }

    /**
     * 通用详情接口
     */
    public function detail(Request $request)
    {
        $data = $request->get();
        v::input($data, array_merge([
            'id' => v::notEmpty()->setName('id'),
        ], $this->validateRules['detail'] ?? []));
        !empty($this->validate) && $this->validate->scene('detail')->check($data);
        $userId = getUserId();
        $info = $this->model::getOne($userId, $data['id']);
        if (empty($info)) {
            return $this->error('记录不存在');
        }
        $this->afterDetail($request, $info);
        return $this->success($info);
    }

    /**
     * 通用详情接口查询后置操作
     * @param Model $data 模型对象
     */
    protected function afterDetail(Request $request, &$data)
    {

    }

    /**
     * 通用列表接口
     */
    public function list(Request $request)
    {
        $data = $request->all();
        $data['user_id'] = getUserId();
        $query = $this->model::getUserQuery($data['user_id'], $this->indexFields);
        $this->beforeList($request, $query);
        foreach ($this->indexFilterFields as $field => $opt) {
            if (isset($data[$field]) && $data[$field] !== '') {
                if (strtolower($opt) == 'in') {
                    $query->whereIn($field, is_string($data[$field])? explode(',', $data[$field]): $data[$field]);
                } elseif (strtolower($opt) == 'not in') {
                    $query->whereNotIn($field, is_string($data[$field])? explode(',', $data[$field]): $data[$field]); 
                } else {
                    $query->where($field, $opt, $data[$field]);
                }
            }
        }
        $sort = [];
        if (!empty($this->indexSortFields)) {
            foreach ($this->indexSortFields as $field) {
                if (isset($data['orderby'][$field]) && in_array($data['orderby'][$field], ['asc', 'desc'])) {
                    $sort[$field] = $data['orderby'][$field];
                }
            }
        }
        empty($sort) && $sort = $this->indexSort;
        foreach ($sort as $field => $opt) {
            $query->orderBy($field, $opt);
        }
        $result = $query->paginate($data['limit'] ?? 15);
        $data = $this->getPaginateData($result);
        $this->afterList($request, $data);
        return $this->success($data);
    }

    /**
     * 通用列表接口查询前置操作
     */
    protected function beforeList(Request $request, \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder &$query)
    {

    }

    /**
     * 通用列表接口查询后置操作
     */
    protected function afterList(Request $request, array &$data)
    {

    }

    /**
     * 通用删除接口
     */
    public function del(Request $request)
    {
        $data = $request->post();
        v::input($data, array_merge([
            'id' => v::notEmpty()->setName('id'),
        ], $this->validateRules['del'] ?? []));
        !empty($this->validate) && $this->validate->scene('del')->check($data);
        $data['user_id'] = getUserId();
        $this->delOpt($request, $data);
        return $this->success();
    }

    /**
     * 通用删除接口自定义删除操作
     */
    protected function delOpt(Request $request, array &$data)
    {
        return $this->model::delById($data['id'], $data['user_id']);
    }
}