<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\Attachment;
use library\IdEncode;
use Respect\Validation\Validator;
use support\Request;

class UploadController extends BaseController
{
    protected $upload;
    protected $noNeedLogin = ['a'];

    public function __construct()
    {
        parent::__construct();
        $this->upload = new \library\Alioss;
    }

    public function genPostSign(Request $request)
    {
        $data = $request->all();
        Validator::input($data, [
            'file_name' => Validator::notEmpty()->setName('文件名'),
            'content_type' => Validator::notEmpty()->setName('content_type'),
        ]);
        $uid = getUserId();
        $hash = $request->input('file_hash');
        if ($hash && $info = Attachment::getByHash($hash, $uid, ['id', 'name', 'type', 'url', 'classify', 'classify_id'])) {
            $data = ['exists_file' => $info];
        } else {
            $data = $this->upload->genPostSign($uid, $data['file_name'], $data['content_type']);
            $data['exists_file'] = null;
        }
        return $this->success($data);
    }

    public function genGetSign(Request $request)
    {
        $object = $request->input('object');
        if (empty($object)) {
            return $this->error('object不能为空');
        }
        $data = $this->upload->genGetSign($object);
        return $this->success($data);
    }

}