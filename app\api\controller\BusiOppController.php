<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\BaseModel;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use app\model\UserChat;
use app\pyapi\service\BusiOppService;
use app\queue\redis\slow\SlowFunctionCall;
use app\wxwork\service\NoticeService;
use app\wxwork\service\WxUserService;
use Respect\Validation\Validator as v;

use support\Request;
use Webman\Exception\BusinessException;

class BusiOppController extends BaseController
{
    protected $indexFields = ['bo_id', 'customer_id', 'title', 'status', 'sort_no', 'created_at'];
    protected $indexFilterFields = [
        'customer_id' => '=',
        'title' => 'like',
    ];
    protected $indexSort = [
        'sort_no' => 'asc',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->model = new BusinessOpportunities;
        $this->validateRules = [
            'add' => [
                'title' => v::notEmpty()->setName('标题'),
            ],
        ];
    }

    protected function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        if (!empty($data['id']) && !empty($data['status']) && $data['status'] != $model['status']) {
            // SlowFunctionCall::send('\app\api\service\BusiOppService', 'pushNewStageAiSuggest', [$data['id'], $data['user_id'], $data['status'], $model['title']]);
        }
        return $model->saveData($data);
    }

    protected function afterDetail(Request $request, &$data)
    {
        $data['todo_suggest'] = BusinessOpportunities::getTodoSuggest($data['status'], $data['suggest_todo_finish_ids']);
    }

    protected function afterList(Request $request, array &$data)
    {
        $uid = getUserId();
        if (!empty($data['list'])) {
            $todoMap = TodoItems::getAfterFirstByBusiness($uid, array_column($data['list'], 'bo_id'), 'id, bo_id, title, due_date');
            foreach ($data['list'] as &$item) {
                $item['todo'] = $todoMap[$item['bo_id']] ?? null;
            }
        }
    }

}