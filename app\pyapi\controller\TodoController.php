<?php

namespace app\pyapi\controller;

use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use app\pyapi\service\LinkService;
use Respect\Validation\Validator as v;
use support\Log;
use support\Request;

class TodoController
{

    public function add(Request $request)
    {
        $data = $request->post();
        v::input($data, [
            'user_id' => v::number()->setName('用户ID'),
            'title' => v::notEmpty()->setName('内容'),
        ]);
        $data['due_date'] = $request->post('due_date', null);
        $data['notify_time'] = $request->post('notify_time', null);
        $data['notify_cycle'] = $request->post('notify_cycle', 0);
        $data['priority'] = $request->post('priority');
        $data['customer_id'] = $request->post('customer_id', 0);
        $data['bo_id'] = $request->post('bo_id', 0);
        $data['description'] = $request->post('description', "");
        $data['priority'] = $data['priority'] ?: '中';
        //判断是否有时间冲突: 暂时不判断
        /*
        if ($data['due_date']) {
            $hi = date('H:i', strtotime($data['due_date']));
            if (!in_array($hi, ['00:00', '23:59'])) {
                $info = TodoItems::getByDate($data['user_id'], $data['due_date'], ['title', 'description']);
                if (!empty($info)) {
                    return json(['code' => 1, 'msg' => '时间冲突，该时间点已经有待办事项了,请换个时间吧', 'data' => [
                        'title' => $info['title'],
                        'description' => $info['description'],
                    ]]);
                }
            }
        }*/
        //截止时间
        if ($data['due_date'] && (strtotime($data['due_date']) !== false)) {
            $data['due_date'] = date('Y-m-d H:i:s', strtotime($data['due_date']));
        }
        //提醒时间
        if ($data['notify_time'] && (strtotime($data['notify_time']) !== false)) {
            $data['notify_time'] = date('Y-m-d H:i:s', strtotime($data['notify_time']));
        }
        if ($data['customer_id']) {
            //判断客户ID是否正确
            $customer = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
            if (empty($customer)) {
                return json(['code' => 1, 'msg' => '客户id错误，客户不存在', 'data' => [
                    'customer_id' => $data['customer_id'],
                ]]);
            }
        }
        if ($data['bo_id']) {
            //判断商机ID是否正确
            $bo = BusinessOpportunities::getOne($data['user_id'], $data['bo_id'], ['bo_id']);
            if (empty($bo)) {
                return json(['code' => 1, 'msg' => '商机id错误，商机不存在', 'data' => [
                    'bo_id' => $data['bo_id'],
                ]]);
            }
        }
        if ($data['priority']) {
            switch ($data['priority']) {
                case '低':
                    $data['priority'] = TodoItems::PRIORITY_LOW;
                    break;
                case '中':
                    $data['priority'] = TodoItems::PRIORITY_MID;
                    break;
                case '高':
                    $data['priority'] = TodoItems::PRIORITY_HIGH;
                    break;
                default:
                    $data['priority'] = TodoItems::PRIORITY_MID;
            }
        }
        //$id = TodoItems::insertGetId($data);
        try {
            $id = TodoItems::addOne($data);
        } catch (\Exception $e) {
            Log::error('添加待办失败：' . $e->getMessage() . ' data=' . var_export($data, true));
            return json(['code' => 1, 'msg' => '添加待办失败']);
        }
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoDetail($id));
        return json(['code' => 0, 'msg' => '成功添加待办',
            'data' => [
                'id' => $id,
                'title' => $data['title'],
                'due_date' => $data['due_date'],
            ],
            'link' => "查看详情：" . $link,
        ]);
    }

    public function detail(Request $request)
    {
        $data = v::input($request->get(), [
            'id' => v::notEmpty()->setName('待办ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $info = TodoItems::getOne($data['user_id'], $data['id']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '，待办数据不存在']);
        }
        $info['priority'] = TodoItems::getPriorityLabel($info['priority']);
        $info['notify_cycle'] = TodoItems::NOTIFY_CYCLE_LABELS[$info['notify_cycle']] ?? '';
        $info['type'] = TodoItems::TYPE_LABELS[$info['type']] ?? '';
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoDetail($data['id']));
        return json(['code' => 0, 'msg' => 'ok', 'data' => $info, 'link' => "查看详情：" . $link]);
    }

    //未完成的待办
    public function actives(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $startDate = $request->post('start_date', '');
        $endDate = $request->post('end_date', '');
        Log::info('start=' . $startDate);
        Log::info('end=' . $endDate);
        $list = TodoItems::getActiveList($data['user_id'],
            ["id", "title", "due_date", 'priority', 'customer_id', 'bo_id', 'type', 'created_at'], $startDate, $endDate);
        foreach ($list as &$item) {
            $item['status'] = "未完成";
            if ($item['due_date'] && (strtotime($item['due_date']) <= time())) {
                $item['status'] = "已逾期";
            }
            $item['due_date'] = TodoItems::genDueDate($item['due_date']);
            $item['priority'] = TodoItems::getPriorityLabel($item['priority']);
            $item['type'] = TodoItems::TYPE_LABELS[$item['type']] ?? '';
        }
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoList());
        return json(['code' => 0, 'msg' => 'ok', 'data' => [
            'total' => count($list),
            'list' => $list,
            'link' => '我的待办列表链接地址：' . $link
        ]]);
    }

    //已完成的待办
    public function completed(Request $request)
    {
        $data = v::input($request->get(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $list = TodoItems::completedList($data['user_id'], ["id", "title", "due_date"]);
        foreach ($list as &$item) {
            $item['due_date'] = TodoItems::genDueDate($item['due_date']);
        }
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoList());
        return json(['code' => 0, 'msg' => 'ok', 'data' => [
            'total' => count($list),
            'list' => $list,
            'link' => '查看所有已完成：' . $link
        ]]);
    }


    //删除待办，支持批量
    public function del(Request $request)
    {
        $data = v::input($request->post(), [
            'id' => v::notEmpty()->setName('待办ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $info = TodoItems::getOne($data['user_id'], $data['id'], ['id']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '待办ID不存在或已被删除']);
        }
        $data['id'] = explode(',', str_replace('，', ',', $data['id']));
        TodoItems::delOne($data['user_id'], $data['id']);
        return json(['code' => 0, 'msg' => '成功删除待办']);
    }

    //更新待办事项时间
    public function updDate(Request $request)
    {
        $data = v::input($request->post(), [
            'id' => v::notEmpty()->setName('待办ID'),
            //'due_date' => v::notEmpty()->setName('待办时间'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $info = TodoItems::getOne($data['user_id'], $data['id'], ['id', 'notify_cycle']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '更新失败，待办不存在']);
        }
        $data = $request->post();
        $params = [];

        if (!empty($data['due_date']) && (strtotime($data['due_date']) !== false)) {
            $params['due_date'] = date('Y-m-d H:i:s', strtotime($data['due_date']));
        } elseif (key_exists('due_date', $data) && is_null($data['due_date'])) {
            $params['due_date'] = null;
        }
        //提醒时间
        if (!empty($data['notify_time']) && (strtotime($data['notify_time']) !== false)) {
            $params['notify_time'] = date('Y-m-d H:i:s', strtotime($data['notify_time']));
        } elseif (key_exists('notify_time', $data) && is_null($data['notify_time'])) {
            $params['notify_time'] = null;
        }
        if (!empty($data['notify_cycle'])) {
            $params['notify_cycle'] = $data['notify_cycle'];
            if ($data['notify_cycle'] != $info['notify_cycle']) {
                $notifyTime = !empty($data['notify_time']) ? date('H:i:s', strtotime($data['notify_time'])) : '09:00:00';
                $date = date('Y-m-d');
                $params['notify_time'] = TodoItems::getNextCycleNotifyTime($date . ' ' . $notifyTime, $data['notify_cycle']);
            }
        } elseif (key_exists('notify_cycle', $data) && ($data['notify_cycle'] === 0 || $data['notify_cycle'] === '0')) {
            $params['notify_cycle'] = 0;
        }

        if (empty($params)) {
            return json(['code' => 1, 'msg' => '更新待办时间失败，可能是时间格式有问题']);
        }

        TodoItems::editOne($data['user_id'], $data['id'], $params);
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoDetail($data['id']));
        return json(['code' => 0, 'msg' => '成功更新待办时间', 'link' => "查看详情：" . $link,]);
    }

    //完成待办
    public function complete(Request $request)
    {
        $data = v::input($request->post(), [
            'id' => v::notEmpty()->setName('待办ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $info = TodoItems::getOne($data['user_id'], $data['id'], ['id']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '更新失败，待办不存在']);
        }
        TodoItems::editOne($data['user_id'], $data['id'], ['is_finished' => 1]);
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoDetail($data['id']));
        return json(['code' => 0, 'msg' => '待办已成功修改为完成', 'link' => "查看详情：" . $link]);
    }

    // 排序
    public function sortAll(Request $request)
    {
        $data = v::input($request->post(), [
            'ids' => v::notEmpty()->setName('ids'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        TodoItems::sortAll($data['user_id'], $data['ids']);
        return json(['code' => 0, 'msg' => '排序成功']);
    }

    //待办关联客户，商机
    public function bindCusBusi(Request $request)
    {
        $data = v::input($request->post(), [
            'todo_id' => v::notEmpty()->setName('todo_id'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $params = [];
        if ($request->post('customer_id')) {
            $params['customer_id'] = $request->post('customer_id');
            $customer = Customers::getOne($data['user_id'], $params['customer_id'], ['customer_id']);
            if (empty($customer)) {
                Log::info('待办关联客户失败，客户ID不存在, data=' . var_export($data, true) . var_export($params, true));
                return json(['code' => 1, 'msg' => '待办关联客户失败，客户ID不存在']);
            }
        }
        if ($request->post('bo_id')) {
            $params['bo_id'] = $request->post('bo_id');
            $busi = BusinessOpportunities::getOne($data['user_id'], $params['bo_id'], ['bo_id']);
            if (empty($busi)) {
                Log::info('待办关联客户失败，商机ID不存在, data=' . var_export($data, true) . '  params=' . var_export($params, true));
                return json(['code' => 1, 'msg' => '待办关联商机失败，商机ID不存在']);
            }
        }
        if (empty($params)) {
            return json(['code' => 1, 'msg' => '待办关联客户或商机失败，关联ID不能为空']);
        }
        TodoItems::editOne($data['user_id'], $data['todo_id'], $params);
        return json(['code' => 0, 'msg' => '待办关联客户或商机成功']);
    }
}