<?php

namespace app\model;

use Closure;
use support\Db;
use support\Model;

class BaseModel extends Model
{

    /**
     * 获取用户查询对象
     * 
     * @return \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder
     */
    public static function getUserQuery($uid, $fields = ['*'])
    {
        $query = (new static)::select($fields);
        $uid && $query->where("user_id", $uid);
        return $query;
    }

    public static function addOne($data)
    {
        return self::insertGetId((new static)->makeBatchData([$data])[0]);
    }

    public static function addAll(array $list, $ignore = false)
    {
        $model = new static;
        return $ignore ? self::insertOrIgnore($model->makeBatchData($list)) : self::insert($model->makeBatchData($list));
    }

    public static function updateById($id, array $data, $userId = null)
    {
        $model = new static;
        $query = $model::whereIn($model->primaryKey, is_array($id)? $id : [$id]);
        $userId && $query->where('user_id', $userId);
        return $query->update($model->makeBatchData([$data])[0]);
    }

    public static function getOne($userId, $id, $fields = ['*'])
    {
        $model = new static;
        return $model::select($fields)
            ->where('user_id', $userId)
            ->where($model->primaryKey, $id)
            ->first();
    }

    /**
     * 获取每个分组的第一条数据
     * 
     * @param string $groupBy 分组字段
     * @param string $orderby 分组排序字段(需要带asc/desc)
     * @param int $uid 用户ID
     * @param array|Closure $wheres 查询条件
     * @param string $fields 查询字段
     */
    public static function getFirstOfEveryGroup(string $groupBy, string $orderby, $uid = null, array|Closure $wheres = [], string $fields = '*')
    {
        $fields .= ", ROW_NUMBER() OVER (PARTITION BY $groupBy ORDER BY $orderby) AS rn";
        $query = $uid ? static::getUserQuery($uid, Db::raw($fields)) : static::selectRaw($fields);
        $wheres && $query->where($wheres);
        var_dump($query->toSql(), $query->getBindings());
        return array_column(Db::table($query, 't')
            ->where('rn', 1)
            ->get()
            ->toArray(), null, $groupBy);
    }

    public static function delById($id, $userId = null)
    {
        $model = new static;
        $query = $model::whereIn($model->primaryKey, is_array($id) ? $id : [$id]);
        $userId && $query->where('user_id', $userId);
        return $query->delete();
    }

    /**
     * 移动受拖拽排序影响的元素(不包括被拖拽的那个元素)
     *
     * @param $userId 用户ID
     * @param int $oldSortNo 旧排序号
     * @param int $newSortNo 新排序号
     * @param string $sortName 排序字段名
     * @return void
     */
    public static function moveSortNo($userId, $oldSortNo, $newSortNo, $sortName = 'sort_no')
    {
        if ($oldSortNo == $newSortNo) { // 未移动
            return;
        }
        if ($newSortNo < $oldSortNo) { // 往前移
            self::where('user_id', $userId)
                ->where($sortName, '>=', $newSortNo)
                ->where($sortName, '<', $oldSortNo)
                ->increment('sort_no');
        } else { // 往后移
            self::where('user_id', $userId)
                ->where('sort_no', '<=', $newSortNo)
                ->where('sort_no', '>', $oldSortNo)
                ->decrement('sort_no');
        }
    }

    public static function getMaxSortNo($userId, $sortName = 'sort_no', $toSql = false)
    {
        $query = self::where('user_id', $userId);
        return $toSql ? $query->selectRaw("MAX($sortName)")->toRawSql() : $query->max($sortName);
    }

    public static function getMinSortNo($userId, $sortName = 'sort_no', $toSql = false)
    {
        $query = self::where('user_id', $userId);
        return $toSql ? $query->selectRaw("MIN($sortName)")->toRawSql() : $query->min($sortName);
    }

    /**
     * 按id的先后顺序重新排序用户的所有元素
     */
    public static function sortAll($userId, array $ids, string $sortName ='sort_no')
    {
        $model = new static;
        $query = $model::getUserQuery($userId);
        $total = $query->count();
        if ($total != count($ids)) { // 元素数量不一致
            $otherIds = (clone $query)
                ->whereNotIn($model->primaryKey, $ids)
                ->orderBy($sortName, 'asc')
                ->pluck($model->primaryKey)
                ->toArray(); // 其他元素的id
            $ids = array_merge($ids, $otherIds); // 合并元素id
        }
        // 生成更新的case语句
        $cases = "(CASE $model->primaryKey";
        foreach ($ids as $index => $id) {
            $cases .= " WHEN $id THEN ". ($index + 1); // 生成case语句
        }
        $cases .= ' ELSE 0 END)';
        // 执行更新
        $query->update([$sortName => Db::raw($cases)]);
    }

    /**
     * 生成case语句
     *
     * @param string $field case条件字段
     * @param array $whenMapThen when=>then
     * @param string $default
     */
    public static function buildCaseSql(string $field, array $whenMapThen, $default = "''")
    {
        $cases = "(CASE $field";
        foreach ($whenMapThen as $when => $then) {
            $cases .= ' WHEN ' . (is_string($when) ? "'$when'" : $when) . ' THEN ' . (is_string($then) ? "'$then'" : $then); // 生成case语句
        }
        $cases .= " ELSE $default END)";
        return $cases;
    }

    /**
     * 数据库创建更新时间序列化
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 根据fillable过滤掉批量写入数据中不能写入的字段
     *
     * @param array $data 批量数据，一个包含多个数据项的数组
     * @return array 返回经过字段过滤后的批量数据
     */
    public function makeBatchData($data)
    {
        $result = [];
        $fields = $this->getFillable();
        if (empty($fields)) {
            return $data;
        }
        foreach ($data as $item) {
            $result[] = array_intersect_key($item, array_flip($fields));
        }
        return $result;
    }
}