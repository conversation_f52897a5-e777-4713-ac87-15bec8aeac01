<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use app\api\service\AttachmentService;
use app\model\ShortLink;
use support\Db;
use support\Redis;
use support\Request;
use Webman\Route;

Route::any('/s/{key}', function (Request $request, $key) { // 短链接
    $info = ShortLink::getByShortUrl($key);
    if (empty($info) || $info['expire_time'] && strtotime($info['expire_time']) < time()) {
        return response('404 Not Found', 404);
    }
    return redirect($info['origin_url']);
});

Route::get('/a/{id}', function (Request $request, $id) { // 附件预览
    return AttachmentService::redirectViewUrlById($id);
});

Route::get('/wxwork/home/<USER>/{id:\d+}', [app\wxwork\controller\HomeController::class, 'callback']);
Route::post('/wxwork/home/<USER>/{id:\d+}', [app\wxwork\controller\HomeController::class, 'receiveMsg']);

// 健康检查接口 - 不需要认证
Route::get('/health', function (Request $request) {
    try {
        Db::select('SELECT 1');
        Redis::ping();
        return response('ok', 200);
    } catch (\Exception $e) {
        return response($e->getMessage(), 500);
    }
});



