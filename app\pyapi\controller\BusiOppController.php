<?php

namespace app\pyapi\controller;

use app\model\TodoItems;
use app\pyapi\service\BusiOppService;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\pyapi\service\LinkService;
use Respect\Validation\Validator as v;
use support\Request;

class BusiOppController
{
    public function add(Request $request)
    {
        $data = v::input($request->post(), [
            'customer_id' => v::number()->setName('客户ID'),
            'user_id' => v::number()->setName('用户ID'),
            'title' => v::notEmpty()->setName('商机标题'),
        ]);
        $data['content'] = $request->post('content', '');
        $data['source'] = $request->post('source', '');
        $data['status'] = $request->post('status', 1);
        $data['budget'] = $request->post('budget', 0);
        $data['notes'] = $request->post('notes', '');
        //查询客户id是否存在
        $customer = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
        if (empty($customer)) {
            return json(['code' => 1, 'msg' => 'customer_id错误，客户数据不存在，可以调用客户列表接口，获取客户id', 'data' => ['customer_id' => $data['customer_id']]]);
        }
        //$id = BusinessOpportunities::insertGetId($data);
        $id = BusinessOpportunities::addOne($data);
        $link = LinkService::genShortLink($data['user_id'], LinkService::genBusiDetail($id));
        return json(['code' => 0, 'msg' => '添加商机成功',
            'data' => [
                'id' => $id,
                'content' => $data['title'],
            ],
            'link' => '点击查看详情：' . $link
        ]);
    }

    //所有商机
    public function list(Request $request)
    {
        $data = v::input($request->get(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);

        $list = BusinessOpportunities::list($data['user_id'], ['bo_id', 'customer_id', 'title', 'content', 'status', 'budget', 'created_at']);
        //@todo 获取客户名称
        foreach ($list as &$item) {
            $item['status'] = BusinessOpportunities::STATUS_MAP[$item['status']] ?? '';
            $item['created_at'] = date('Y-m-d', strtotime($item['created_at']));
        }
        $link = LinkService::genShortLink($data['user_id'], LinkService::genBusiList());
        return json(['code' => 0, 'msg' => 'ok', 'total' => count($list), 'data' => $list, 'link' => '点击查看我的所有商机列表：' . $link]);
    }

    //修改商机
    public function edit(Request $request)
    {
        $data = v::input($request->post(), [
            'bo_id' => v::number()->setName('商机ID'),
            'user_id' => v::number()->setName('用户ID'),
        ]);
        $id = $data['bo_id'];
        $info = BusinessOpportunities::getOne($data['user_id'], $id, ['bo_id']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '商机数据不存在', 'data' => ['bo_id' => $id]]);
        }
        $updates = [
            'user_id' => $data['user_id'],
        ];
        if ($request->post('content')) {
            $updates['content'] = trim($request->post('content'));
        }
        if ($request->post('title')) {
            $updates['title'] = trim($request->post('title'));
        }
        if ((int)$request->post('customer_id') > 0) {
            $customer = Customers::getOne($data['user_id'], $request->post('customer_id'), ['customer_id']);
            if (empty($customer)) {
                return json(['code' => 1, 'msg' => '客户数据不存在', 'data' => ['customer_id' => $request->post('customer_id')]]);
            }
        }
        if ($request->post('status')) {
            $updates['status'] = trim($request->post('status'));
            $aiResult = BusiOppService::aiGetStageAndSuggest($id, $data['user_id']);
            $finishedSuggestIds = [];
            if (!empty($aiResult)) {
                // isset($aiResult['current_stage']) && isset(BusinessOpportunities::STATUS_MAP[$aiResult['current_stage']]) && $updates['status'] = $aiResult['current_stage'];
                foreach ($aiResult['stage_goals'] ?? [] as $item) {
                    !empty($item['goal_id']) && !empty($item['is_finished']) && $finishedSuggestIds[] = $item['goal_id'];
                }
                unset($aiResult['current_stage'], $aiResult['stage_goals']);
            }
            // 当前阶段之前的推荐待办全部改成完成
            $finishedSuggestIds = array_unique(array_merge($finishedSuggestIds, array_column(BusinessOpportunities::getAllBeforeTodoSuggest($updates['status']), 'id')));
            $updates['suggest_todo_finish_ids'] = implode(',', $finishedSuggestIds);
        }
        if ($request->post('budget')) {
            $updates['budget'] = trim($request->post('budget'));
        }
        if ($request->post('notes')) {
            $updates['notes'] = trim($request->post('notes'));
        }
        if (!empty($updates)) {
            BusinessOpportunities::updOne($data['user_id'], $id, $updates);
            $link = LinkService::genShortLink($data['user_id'], LinkService::genBusiDetail($id));
            $result = ['code' => 0, 'msg' => '更新商机信息成功', 'data' => $updates, 'link' => '点击查看详情：' . $link];
            if (!empty($aiResult)) {
                $result['msg'] .= ', 需要将follow_up_guidance中的内容整理发送给用户';
                $result['follow_up_guidance'] = $aiResult;
            }
            return json($result);
        }
        return json(['code' => 0, 'msg' => '没有信息要更新']);
    }

    //商机关联客户
    public function bindCustomer(Request $request)
    {
        $data = v::input($request->post(), [
            'customer_id' => v::notEmpty()->setName('客户ID'),
            'bo_id' => v::notEmpty()->setName('商机ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $boInfo = BusinessOpportunities::getOne($data['user_id'], $data['bo_id'], ['bo_id']);
        if (empty($boInfo)) {
            return json(['code' => 1, 'msg' => '商机数据不存在', 'data' => ['bo_id' => $data['bo_id']]]);
        }
        $cusInfo = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
        if (empty($cusInfo)) {
            return json(['code' => 1, 'msg' => '客户数据不存在', 'data' => ['customer_id' => $data['customer_id']]]);
        }
        BusinessOpportunities::updOne($data['user_id'], $data['bo_id'], ['customer_id' => $data['customer_id']]);
        $link = LinkService::genShortLink($data['user_id'], LinkService::genBusiDetail($data['bo_id']));
        return json(['code' => 0, 'msg' => '商机和客户关联成功', 'link' => '点击查看商机详情：' . $link]);

    }

    //删除商机
    public function del(Request $request)
    {
        $data = v::input($request->post(), [
            'bo_id' => v::notEmpty()->setName('商机ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
//        $info = BusinessOpportunities::getOne($data['user_id'], $data['bo_id'], ['bo_id']);
//        if (empty($info)) {
//            return json(['code' => 1, 'msg' => '商机数据不存在', 'data' => ['bo_id' => $data['bo_id']]]);
//        }
        $data['bo_id'] = explode(',', str_replace('，', ',', $data['bo_id']));

        BusinessOpportunities::delById($data['bo_id'], $data['user_id']);
        return json(['code' => 0, 'msg' => ' 商机删除成功']);
    }

    //商机详情
    public function detail(Request $request)
    {
        $data = v::input($request->get(), [
            'bo_id' => v::notEmpty()->setName('商机ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $info = BusinessOpportunities::getOne($data['user_id'], $data['bo_id'], ['*']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '商机数据不存在', 'data' => ['bo_id' => $data['bo_id']]]);
        }
        $link = LinkService::genShortLink($data['user_id'], LinkService::genBusiDetail($data['bo_id']));
        return json(['code' => 0, 'msg' => 'ok', 'data' => $info, 'link' => '点击查看详情：' . $link]);
    }

    // 排序
    public function sortAll(Request $request)
    {
        $data = v::input($request->post(), [
            'ids' => v::notEmpty()->setName('ids'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        BusinessOpportunities::sortAll($data['user_id'], $data['ids']);
        return json(['code' => 0, 'msg' => '排序成功']);
    }

    //添加商机跟进记录
    public function addInteract(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
            'bo_id' => v::notEmpty()->setName('商机id'),
            'title' => v::notEmpty()->setName('标题'),
            'content' => v::notEmpty()->setName('内容'),
            'date' => v::notEmpty()->setName('日期'),
            'is_finished' => v::notEmpty()->setName('是否完成'),
        ]);
        $info = BusinessOpportunities::getOne($data['user_id'], $data['bo_id'], ['customer_id']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '商机数据不存在', 'data' => ['bo_id' => $data['bo_id']]]);
        }
        $id = TodoItems::addOne([
            'user_id' => $data['user_id'],
            'bo_id' => $data['bo_id'],
            'customer_id' => $info['customer_id'],
            'title' => $data['title'],
            'description' => $data['content'],
            'due_date' => date('Y-m-d H:i:s', strtotime($data['date'])),
            'priority' => 20,
            'is_finished' => $data['is_finished'],
            'type' => 1,
        ]);
        $link = LinkService::genShortLink($data['user_id'], LinkService::genTodoDetail($id));
        return json(['code' => 0, 'msg' => '跟进记录添加成功', 'link' => $link]);
    }

}