<?php

namespace app;

use Workerman\Connection\TcpConnection;

/**
 * 推送消息给企微机器人
 */
class WechatPusher
{
    protected static $connection_wx;

    public function onConnect(TcpConnection $connection)
    {
        echo "onConnect\n";
    }

    public function onWebSocketConnect(TcpConnection $connection, $http_buffer)
    {
        echo "onWebSocketConnect\n";
    }

    public function onMessage(TcpConnection $connection, $data)
    {
        if ($data == 'wechat-login') {
            self::$connection_wx = $connection;
            return;
        }

        //心跳检测
        if ($data == 'ping') {
            $connection->send('pong');
            return;
        }

        //$connection->send($data);
        if (self::$connection_wx) {
            self::$connection_wx->send($data);
            sleep(1); //加入延时，避免高频推送，导致封号
        }
    }

    public function onClose(TcpConnection $connection)
    {
        echo "onClose\n";
    }
}