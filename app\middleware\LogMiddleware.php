<?php

namespace app\middleware;

use support\Log;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class LogMiddleware implements MiddlewareInterface
{
    /**
     * 不记录日志的路径
     */
    private array $excludePaths = [
        '/health',
        '/favicon.ico',
    ];

    /**
     * 敏感字段，在日志中会被脱敏
     */
    private array $sensitiveFields = [
        'password',
        'token',
        'authorization',
        'secret',
        'key',
        'private_key',
        'access_token',
        'refresh_token',
    ];

    public function process(Request $request, callable $handler): Response
    {
        $startTime = microtime(true);
        $requestId = $this->generateRequestId();
        // 将请求ID存入请求对象
        $request->requestId = $requestId;
        
        // 记录请求日志
        $this->logRequest($request, $requestId);
        
        // 处理请求
        $response = $handler($request);
        
        // 计算响应时间
        $responseTime = round((microtime(true) - $startTime) * 1000, 2);
        
        // 记录响应日志
        $this->logResponse($request, $response, $requestId, $responseTime);
        
        return $response;
    }

    /**
     * 记录请求日志
     */
    private function logRequest(Request $request, string $requestId): void
    {
        // 检查是否需要排除
        if ($this->shouldExclude($request->path())) {
            return;
        }

        $logData = [
            // 'request_id' => $requestId,
            'type' => 'request',
            'method' => $request->method(),
            'uri' => $request->uri(),
            // 'path' => $request->path(),
            // 'query' => $request->get(),
            'headers' => $this->filterSensitiveData($request->header()),
            'body' => $this->getRequestBody($request),
            'ip' => $request->getRealIp(),
            'user_agent' => $request->header('user-agent', ''),
        ];

        Log::channel('api')->info('API Request', $logData);
    }

    /**
     * 记录响应日志
     */
    private function logResponse(Request $request, Response $response, string $requestId, float $responseTime): void
    {
        // 检查是否需要排除
        if ($this->shouldExclude($request->path())) {
            return;
        }

        $logData = [
            // 'request_id' => $requestId,
            'type' => 'response',
            'method' => $request->method(),
            // 'uri' => $request->uri(),
            'path' => $request->path(),
            'status_code' => $response->getStatusCode(),
            'response_time_ms' => $responseTime,
            'response_size' => strlen($response->rawBody()),
            'user_id' => getUserId(),
        ];

        // 记录响应内容（可选，根据需要开启）
        if ($this->shouldLogResponseBody($request, $response)) {
            $responseBody = $response->rawBody();
            if ($responseBody) {
                $logData['response_body'] = $this->truncateResponseBody($responseBody);
            }
        }

        // 根据响应时间和状态码确定日志级别
        $logLevel = $this->getLogLevel($response->getStatusCode(), $responseTime);
        
        Log::channel('api')->log($logLevel, 'API Response', $logData);
    }

    /**
     * 获取请求体内容
     */
    private function getRequestBody(Request $request): array
    {
        $body = [];
        
        // 获取POST数据
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            $contentType = $request->header('content-type', '');
            
            if (strpos($contentType, 'application/json') !== false) {
                $rawBody = $request->rawBody();
                if ($rawBody) {
                    $jsonData = json_decode($rawBody, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $body = $this->filterSensitiveData($jsonData);
                    }
                }
            } else {
                $body = $this->filterSensitiveData($request->post());
            }
        }
        
        return $body;
    }

    /**
     * 过滤敏感数据
     */
    private function filterSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);
            
            // 检查是否为敏感字段
            foreach ($this->sensitiveFields as $sensitiveField) {
                if (strpos($lowerKey, $sensitiveField) !== false) {
                    $data[$key] = $this->maskSensitiveValue($value);
                    break;
                }
            }
            
            // 递归处理数组
            if (is_array($value)) {
                $data[$key] = $this->filterSensitiveData($value);
            }
        }
        
        return $data;
    }

    /**
     * 脱敏敏感值
     */
    private function maskSensitiveValue($value): string
    {
        if (is_string($value)) {
            $length = strlen($value);
            if ($length <= 4) {
                return str_repeat('*', $length);
            }
            return substr($value, 0, 2) . str_repeat('*', $length - 4) . substr($value, -2);
        }
        
        return '***';
    }

    /**
     * 是否应该排除此路径
     */
    private function shouldExclude(string $path): bool
    {
        foreach ($this->excludePaths as $excludePath) {
            if ($path === $excludePath || strpos($path, $excludePath) === 0) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 是否应该记录响应体
     */
    private function shouldLogResponseBody(Request $request, Response $response): bool
    {
        // 只记录错误响应或特定状态码的响应体
        $statusCode = $response->getStatusCode();
        
        // 记录4xx和5xx错误
        if ($statusCode >= 400) {
            return true;
        }
        
        // 可以根据需要添加其他条件
        return true;
    }

    /**
     * 截断响应体内容
     */
    private function truncateResponseBody(string $body, int $maxLength = 1000): string
    {
        if (strlen($body) <= $maxLength) {
            return $body;
        }
        
        return mb_substr($body, 0, $maxLength) . '... [truncated]';
    }

    /**
     * 根据状态码和响应时间确定日志级别
     */
    private function getLogLevel(int $statusCode, float $responseTime): string
    {
        // 5xx错误使用error级别
        if ($statusCode >= 500) {
            return 'error';
        }
        
        // 4xx错误使用warning级别
        if ($statusCode >= 400) {
            return 'warning';
        }
        
        // 响应时间过长使用warning级别
        if ($responseTime > 3000) { // 3秒
            return 'warning';
        }
        
        // 其他情况使用info级别
        return 'info';
    }

    /**
     * 生成请求ID
     */
    private function generateRequestId(): string
    {
        return uniqid('req_', true);
    }
}
