<?php

namespace app\model;

use support\Model;

class CustomerInteractions extends Model
{
    protected $table = 'customer_interactions';
    protected $primaryKey = 'id';
    public $timestamps = true;


    public static function addOne($data)
    {
        return self::insertGetId($data);
    }

    public static function list($uid, $customerId, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->where('customer_id', $customerId)
            ->get()->toArray();
    }

}