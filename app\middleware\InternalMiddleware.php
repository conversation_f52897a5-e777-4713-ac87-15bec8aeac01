<?php

namespace app\middleware;

use support\Log;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class InternalMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        if (env('APP_ENV') == 'dev') {
            return $handler($request);
        }
        //是否是内网ip
        if (!in_array($request->getRealIp(), ['*************', '**********', '***************'])) {
            Log::error($request->getRealIp());
            return response('非法访问', 403);
        }
        return $handler($request);
    }
}