<?php

namespace app\model;

use app\wxwork\service\WxUserService;
use Illuminate\Database\Eloquent\SoftDeletes;
use support\Model;

class UserChat extends BaseModel
{
    use SoftDeletes;

    protected $table = 'user_chat';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $fillable = ['user_id', 'content', 'content_type', 'attachment_id', 'session_id', 'task_id', 'is_robot', 'is_like', 'is_dislike', 'dislike_reason'];
    const INTRODUCE = "您好，我是您的智能销售助手 Ivy 😄

我能帮您：
 - 管理客户和商机，记录并跟进销售进展
 - 创建和提醒各类待办事项
 - 快速查询公司、联系人、文件和资料
 - 搜索企业、产品或客户的最新动态
 - 提供个性化的销售建议和跟进话术

建议您先上传常用销售资料（比如公司介绍、产品介绍、价格清单、解决方案模板、合同模板、报价单模板等），方便后续随时调用。

请问您怎么称呼？";

    public static function addOne($data)
    {
        return self::insertGetId($data);
    }

    public static function AddIntroduce($userId, $sessionId = '')
    {
        return self::insertGetId([
            'user_id' => $userId,
            'session_id' => $sessionId ?: WxUserService::getSessionId($userId),
            'content' => json_encode([['role' => 'assistant', 'content' => self::INTRODUCE]], JSON_UNESCAPED_UNICODE),
            'is_robot' => 1,
        ]);
    }

    public static function getList($userId, $limit, $fields = ['*'])
    {
        return self::select($fields)
            ->where('user_id', $userId)
            ->orderBy('id', 'desc')
            ->limit($limit)
            ->get()->toArray();
    }

    public static function empty($uid)
    {
        return self::where("user_id", $uid)
            ->delete();
    }

    public static function getMessages($userId, $limit)
    {
        $messages = static::getList($userId, $limit, ['content', 'is_robot']);
        foreach ($messages as &$item) {
            $item['role'] = $item['is_robot'] ? 'assistant' : 'user';
            unset($item['is_robot']);
        }
        return array_reverse($messages);
    }

    public static function getLastMsg($userId, $fields = ['*'], $sessionId = '')
    {
        $query = self::select($fields)
            ->where('user_id', $userId);
        $sessionId && $query->where('session_id', $sessionId);
        return $query->orderBy('id', 'desc')->first();
    }

    public static function hasUserMsg($userId, $sessionId = '')
    {
        $query = self::getUserQuery($userId, ['id'])->where('is_robot', 0);
        $sessionId && $query->where('session_id', $sessionId);
        return !empty($query->first());
    }

    public static function getMsgByTaskId($userId, $taskId, $fields = ['*'], $isUuid = true)
    {
        if ($isUuid) {
            $taskId = TaskResult::getOneByTaskId($userId, $taskId, ['id']);
            if (empty($taskId)) {
                return null;
            }
            $taskId = $taskId['id'];
        }
        return self::getUserQuery($userId, $fields)
            ->where('task_id', $taskId)
            ->first();
    }

    // 从json消息对象中提取文本消息
    public static function extractTextFromJson($jsonContent)
    {
        $contentData = json_decode($jsonContent, true);
        if (empty($contentData)) { // 不是json格式的消息(用户普通文本消息)
            return $jsonContent;   
        }
        foreach ($contentData as $item) {
            if (isset($item['role']) && $item['role'] == 'assistant') { // ai消息
                return $item['content'];
            }
            if (isset($item['content_type']) && $item['content_type'] == 'str') { // 用户文件消息
                return $item['content'];
            }
        }
        return $jsonContent;
    }

}