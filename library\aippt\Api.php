<?php

namespace library\aippt;

use library\redisKeys;
use support\Log;
use support\Redis;

require 'Event.php';
require 'SseClient.php';
require 'HttpUtils.php';

class Api
{

    const BASE_URL = "https://docmee.cn";
    const API_KEY = "ak_6MzTmgTpp6T6rspv6y";

    public static function getToken($uid)
    {
        $key = redisKeys::AI_PPT_TOKEN . $uid;
        $apiToken = Redis::get($key);
        if (empty($apiToken)) {
            $apiToken = static::createApiToken(static::API_KEY, $uid, null);
            Redis::setEx($key, 7200, $apiToken);
        }
        Log::info('token=' . $apiToken);
        return $apiToken;
    }

    public static function createApiToken($apiKey, $uid, $limit)
    {
        $url = Api::BASE_URL . "/api/user/createApiToken";
        $headers = [
            "Api-Key" => $apiKey
        ];
        $body = json_encode([
            "uid" => $uid,
            "limit" => $limit
        ]);
        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("创建apiToken失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("创建apiToken异常：" . $json["message"]);
        }
        return $json["data"]["token"];
    }

    public static function generateOutline($apiToken, $subject, $prompt = null, $dataUrl = null)
    {
        $url = Api::BASE_URL . "/api/ppt/generateOutline";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "subject" => $subject,
            "prompt" => $prompt,
            "dataUrl" => $dataUrl
        ]);
        $sb = [];
        $resp = HttpUtils::postSse($url, $headers, $body, function ($data) use (&$sb) {
            $json = json_decode($data, true);
            if (array_key_exists("status", $json) && $json["status"] == -1) {
                throw new \RuntimeException("生成大纲异常：" . $json["error"]);
            }
            if (array_key_exists("text", $json)) {
                $text = $json['text'];
                array_push($sb, $text);
                //echo $text;
            }
        });
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("生成大纲失败，httpStatus=" . $resp["statusCode"]);
        }
        if (strpos($resp["contentType"], 'application/json') !== false) {
            $json = json_decode($resp["text"], true);
            throw new \RuntimeException("生成大纲异常：" . $json["message"]);
        }
        return implode('', $sb);
    }

    public static function generateContent($apiToken, $outlineMarkdown, $prompt = null, $dataUrl = null)
    {
        $url = Api::BASE_URL . "/api/ppt/generateContent";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "outlineMarkdown" => $outlineMarkdown,
            "prompt" => $prompt,
            "dataUrl" => $dataUrl
        ]);
        $sb = [];
        $resp = HttpUtils::postSse($url, $headers, $body, function ($data) use (&$sb) {
            $json = json_decode($data, true);
            if (array_key_exists("status", $json) && $json["status"] == -1) {
                throw new \RuntimeException("生成大纲内容异常：" . $json["error"]);
            }
            if (array_key_exists("text", $json)) {
                $text = $json['text'];
                array_push($sb, $text);
                //echo $text;
            }
        });
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("生成大纲内容失败，httpStatus=" . $resp["statusCode"]);
        }
        if (strpos($resp["contentType"], 'application/json') !== false) {
            $json = json_decode($resp["text"], true);
            throw new \RuntimeException("生成大纲内容异常：" . $json["message"]);
        }
        return implode('', $sb);
    }

    public static function randomOneTemplateId($apiToken, $neqId = [], $style = "", $themeColor = "")
    {
        $url = Api::BASE_URL . "/api/ppt/randomTemplates";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "size" => 1,
            "filters" => [
                "type" => 1,
                'neq_id' => $neqId,
                "style" => $style ?: null,
                "themeColor" => $themeColor ?: null,
            ]
        ]);
        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("创建模板失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("创建模板异常：" . $json["message"]);
        }
        if (empty($json["data"])) {
            throw new \RuntimeException("没有可用的模板,换个风格/颜色吧");
        }
        return $json["data"][0]["id"];
    }

    public static function generatePptx($apiToken, $templateId, $markdown, $pptxProperty = false)
    {
        $url = Api::BASE_URL . "/api/ppt/generatePptx";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "templateId" => $templateId,
            "outlineContentMarkdown" => $markdown,
            "pptxProperty" => $pptxProperty
        ]);
        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("生成PPT失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("生成PPT异常：" . $json["message"]);
        }
        return $json["data"]["pptInfo"];
    }

    public static function downloadPptx($apiToken, $id)
    {
        $url = Api::BASE_URL . "/api/ppt/downloadPptx";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "id" => $id
        ]);
        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("下载PPT失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("下载PPT异常：" . $json["message"]);
        }
        return $json["data"]["fileUrl"];
    }

    public static function directGeneratePptx($apiToken, $stream, $templateId, $subject, $prompt = null, $dataUrl = null, $pptxProperty = false)
    {
        $url = Api::BASE_URL . "/api/ppt/directGeneratePptx";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "stream" => $stream,
            "templateId" => $templateId,
            "subject" => $subject,
            "prompt" => $prompt,
            "dataUrl" => $dataUrl,
            "pptxProperty" => $pptxProperty
        ]);
        if ($stream) {
            $pptInfo = [];
            $resp = HttpUtils::postSse($url, $headers, $body, function ($data) use (&$pptInfo) {
                $json = json_decode($data, true);
                if (array_key_exists("status", $json) && $json["status"] == -1) {
                    throw new \RuntimeException("生成大纲内容异常：" . $json["error"]);
                }
                if (array_key_exists("status", $json) && $json["status"] == 4 && array_key_exists("result", $json)) {
                    array_push($pptInfo, $json["result"]);
                }
                if (array_key_exists("text", $json)) {
                    //echo $json['text'];
                }
            });
            if ($resp["statusCode"] != 200) {
                throw new \RuntimeException("生成PPT失败，httpStatus=" . $resp["statusCode"]);
            }
            if (strpos($resp["contentType"], 'application/json') !== false) {
                $json = json_decode($resp["text"], true);
                throw new \RuntimeException("生成PPT异常：" . $json["message"]);
            }
            return $pptInfo[0];
        } else {
            $resp = HttpUtils::postJson($url, $headers, $body);
            if ($resp["statusCode"] != 200) {
                throw new \RuntimeException("生成PPT失败，httpStatus=" . $resp["statusCode"]);
            }
            $json = json_decode($resp["text"], true);
            if ($json["code"] != 0) {
                throw new \RuntimeException("生成PPT异常：" . $json["message"]);
            }
            return $json["data"]["pptInfo"];
        }
    }

    //更换PPT模板
    public static function updateTemplate($apiToken, $pptId, $templateId)
    {
        $url = Api::BASE_URL . "/api/ppt/updatePptTemplate";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "templateId" => $templateId,
            "pptId" => $pptId,
            "sync" => false,
        ]);
        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("更换PPT模板失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("更换PPT模板异常：" . $json["message"]);
        }
        return $json["data"];
    }

    //保存 PPT
    public static function savePptx($apiToken, $pptId, $pptxProperty)
    {
        $url = Api::BASE_URL . "/api/ppt/savePptx";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode([
            "id" => $pptId,
            "pptxProperty" => $pptxProperty,
            "drawPptx" => true,
            "drawCover" => true,
        ]);
        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("保存PPT失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("保存PPT异常：" . $json["message"]);
        }
        return $json["data"];
    }

}