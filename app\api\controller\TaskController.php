<?php

namespace app\api\controller;

use app\api\service\AiTaskService;
use app\controller\BaseController;
use app\model\MainAgentSessions;
use app\model\UserChat;
use app\wxwork\service\WxUserService;
use Respect\Validation\Validator as v;
use support\Redis;
use support\Request;
use Webman\RateLimiter\Annotation\RateLimiter;

class TaskController extends BaseController
{

    //增加频率限制 - uid 限制
    #[RateLimiter(limit: 1, ttl: 2, key: RateLimiter::UID, message: '请求太频繁')]
    public function create(Request $request)
    {
        $data = $request->all();
        v::input($data, [
            'query' => v::notEmpty()->setName('query'),
        ]);
        $userId = getUserId();
        if (in_array($data['query'], ['清空聊天记录', '清理所有聊天记录'])) {
            MainAgentSessions::delByUserId($userId);
            UserChat::empty($userId);
            UserChat::AddIntroduce($userId, $data['session_id'] ?? '');
            return $this->success();
        }
        $res = AiTaskService::createTask($userId, $data['query'], $data['session_id'] ?? '');
        return $res ? $this->success(['task_id' => $res]) : $this->error();
    }

    public function stop(Request $request)
    {
        $data = $request->all();
        v::input($data, [
            'task_id' => v::notEmpty()->setName('task_id'),
        ]);
        Redis::set('ai_task_stop:' . $data['task_id'], 1, 'ex', 3600);
        return $this->success();
    }

    public function contextClear(Request $request)
    {
        $userId = getUserId();
        $sessionId = $request->input('session_id', '');
        $lastMsg = UserChat::getLastMsg($userId, ['content_type', 'content'], $sessionId);
        if (empty($lastMsg) || $lastMsg['content_type'] == 'operation' && $lastMsg['content'] == 'contextClear') { // 防止重复
            return $this->success();
        }
        MainAgentSessions::delByUserId($userId);
        UserChat::addOne(['user_id' => $userId, 'session_id' => $sessionId ?: WxUserService::getSessionId($userId), 'content' => 'contextClear', 'content_type' => 'operation']);
        return $this->success();
    }

    public function results(Request $request)
    {
        $data = $request->all();
        $data['user_id'] = getUserId();
        v::input($data, [
            'id' => v::notEmpty()->setName('id'),
        ]);
        if (empty($data['stream'])) {
            return $this->success(AiTaskService::getResults($data['user_id'], $data['id']));
        }
        // stream响应
        $data['res_index'] = 0;
        return $this->stream($request, function (&$result) use (&$data) {
            $result = array_slice(AiTaskService::getResults($data['user_id'], $data['id']) ?: [], $data['res_index']);
            $data['res_index'] += count($result);
            return $result && AiTaskService::isFinished(end($result));
        });
    }

}