<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;
use library\tool;

class ShortLink extends BaseModel
{
    use SoftDeletes;

    protected $fillable = ['platform', 'short_url', 'origin_url', 'expire_time'];
    protected $table = 'short_link';
    protected $primaryKey = 'id';
    public $timestamps = true;

    public static function getByShortUrl($shortUrl, $fields = ['*'])
    {
        $info = self::where('short_url', $shortUrl)->first($fields);
        if (!empty($info)) {
            $info->short_url = trim(env('API_DOMAIN'), '/ ') . '/s/' . trim($info->short_url, '/ ');
            $info->origin_url = trim(env('WEB_DOMAIN'), '/ ') . '/' . trim($info->origin_url, '/ ');
        }
        return $info;
    }

    /**
     * 生成短链接
     * @param $userId 用户ID
     * @param $originUrl 原始链接路径
     * @param $expireTime 短链失效日期时间, 默认不失效
     * @param $platform 短链平台 1:系统
     * @return string 完整短链链接
     */
    public static function genShortUrl($userId, $originUrl, $expireTime = null, $platform = 1)
    {
        //$token = User::genToken($userId);
        //$originUrl = $originUrl . (strpos($originUrl, '?') === false ? '?' : '&') . http_build_query(['authorization' => $token]);
        while (true) {
            $shortUrl = tool::randomString(8);
            $result = self::insertOrIgnore(['short_url' => $shortUrl, 'origin_url' => $originUrl, 'expire_time' => $expireTime, 'platform' => $platform]);
            if ($result) {
                return self::getCompleteShortUrl($shortUrl);
            }
        }
    }

    public static function getCompleteShortUrl($shortUrl)
    {
        return trim(env('API_DOMAIN'), '/ ') . '/s/' . trim($shortUrl, '/ ');
    }
}