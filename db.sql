CREATE TABLE `customers`
(
    `customer_id`        int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`            int(10) unsigned NOT NULL,
    `company_name`       varchar(200) NOT NULL DEFAULT '' COMMENT '企业名称\n',
    `company_short_name` varchar(50)  NOT NULL DEFAULT '' COMMENT '企业简称',
    `contact_name`       varchar(100) NOT NULL DEFAULT '' COMMENT '联系人',
    `customer_level`     tinyint(4) NOT NULL DEFAULT '0' COMMENT '客户级别：10 潜在客户，20 普通客户，30 重点客户',
    `contact_remark`     varchar(255) NOT NULL DEFAULT '' COMMENT '联系人备注',
    `contact_title`      varchar(100) NOT NULL DEFAULT '' COMMENT '联系人职位',
    `email`              varchar(200) NOT NULL DEFAULT '',
    `phone`              varchar(50)  NOT NULL DEFAULT '',
    `address`            varchar(255) NOT NULL DEFAULT '',
    `industry`           varchar(100) NOT NULL DEFAULT '' COMMENT '行业',
    `status`             varchar(50)  NOT NULL DEFAULT 'active' COMMENT '状态',
    `notes`              varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `created_at`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`         datetime              DEFAULT NULL,
    PRIMARY KEY (`customer_id`),
    KEY                  `idx_user_id` (`user_id`),
    KEY                  `idx_company_name` (`company_name`),
    KEY                  `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `todo_items`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`     int(11) unsigned NOT NULL DEFAULT '0',
    `customer_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
    `bo_id`       int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商业id',
    `title`       varchar(255) NOT NULL COMMENT '内容',
    `description` varchar(255) NOT NULL DEFAULT '',
    `due_date`    datetime              DEFAULT NULL COMMENT '截止日期',
    `priority`    tinyint(11) NOT NULL DEFAULT '0' COMMENT '优先级: 10 低，20 中，30 高',
    `is_finished` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否完成',
    `sort_no`     int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
    `created_at`  datetime              DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`  datetime              DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY           `idx_user_id` (`user_id`) USING BTREE,
    KEY           `idx_customer_id` (`customer_id`) USING BTREE,
    KEY           `idx_bo_id` (`bo_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `business_opportunities`
(
    `bo_id`       int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`     int(11) unsigned NOT NULL DEFAULT '0',
    `customer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '客户id',
    `content`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商机信息',
    `source`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商机来源',
    `status`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '当前阶段：potential_customer，demand_confirm，project_design，business_negotiation，sign_maintain，win_order_close，dropped_order_close',
    `budget`      decimal(10, 2)                                                         DEFAULT NULL COMMENT '预算',
    `notes`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注信息',
    `created_at`  datetime                                                               DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`  datetime                                                               DEFAULT NULL,
    PRIMARY KEY (`bo_id`) USING BTREE,
    KEY           `idx_user_id` (`user_id`) USING BTREE,
    KEY           `idx_customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE `attachment`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `parent_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '所属上级id',
    `user_id`     int(10) unsigned NOT NULL,
    `classify`    tinyint(4) NOT NULL DEFAULT '0' COMMENT '分类：1 客户文件，2 商机文件，3 产品介绍，4 项目资料',
    `classify_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '所属分类id，如客户id或商机id',
    `name`        varchar(255) COLLATE utf8mb4_general_ci                      NOT NULL COMMENT '文件或目录名',
    `type`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件类型：dir为目录',
    `url`         varchar(255) COLLATE utf8mb4_general_ci                      NOT NULL DEFAULT '' COMMENT '文件url',
    `size`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小（字节）',
    `created_at`  datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`  datetime                                                              DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY           `idx_user_id` (`user_id`) USING BTREE,
    KEY           `idx_classify_classify_id` (`classify`,`classify_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='附件管理';

CREATE TABLE `user`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT,
    `name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
    `mobile`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
    `third_id`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '企微用户id ',
    `created_at` datetime                                                      DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` datetime                                                      DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY          `third_id` (`third_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `user_ppt`
(
    `id`         int unsigned NOT NULL AUTO_INCREMENT,
    `user_id`    int unsigned NOT NULL DEFAULT '0',
    `ppt_id`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'pptid',
    `created_at` datetime                                                               DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime                                                               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` datetime                                                               DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY          `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;