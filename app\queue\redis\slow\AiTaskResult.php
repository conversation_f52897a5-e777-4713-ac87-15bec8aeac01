<?php

namespace app\queue\redis\slow;

use app\wechat\service\AiTaskService;
use library\redisKeys;
use support\Log;
use support\Redis;
use Webman\RedisQueue\Consumer;

/**
 * 企微bot - 处理ai任务的结果
 */
class AiTaskResult implements Consumer
{
    // 要消费的队列名
    public $queue = 'ai-task';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';

    /**
     * 消费
     * data:  user_id, user_wxid, content, query
     **/
    public function consume($data)
    {
        //
        //Log::info('开始消费队列---');
        //Log::info(var_export($data, true));

        if (empty($data['task_id']) || empty($data['user_id'])) {
            Log::error('ai-task队列，参数错误 data=' . var_export($data, true));
            return;
        }


        $taskId = $data['task_id'];
        $userId = $data['user_id'];

        //get结果
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        $count = 1;

        while (true) {
            if ($count >= 600) {
                return $this->end('执行超时');
            }
            //是否完成
            $res = Redis::lIndex($key, -1);
            if ($res) {
                $res = json_decode($res, true);
                if (!empty($res['event']) && $res['event'] == 'RunCompleted') {
                    $isDone = 1;
                    $resContent = trim($res['content']);
                    break;
                }
            }
            sleep(1);
            $count++;
        }
        if ($isDone == 1) {
            //结束了，需要将结果让ai重新总结一下
            AiTaskService::finished($userId, $data['user_wxid'], $taskId, $resContent);
        }
    }


    public function end($taskId)
    {
        Log::info('queue  success----');
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        Redis::rPush($key, json_encode(['event' => 'RunCompleted', 'content' => '执行超时']));
        return false;
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        // 无需反序列化
        Log::error('ai-task队列消费失败，data=【' . var_export($package, true) . '】 error=【' . $e->getMessage() . '】');
    }
}