<?php

namespace app\wxwork\controller;

use app\model\MainAgentSessions;
use app\model\User;
use app\model\UserChat;
use app\model\UserWxwork;
use app\model\WxworkConfig;
use app\pyapi\service\LinkService;
use app\wxwork\service\AiTaskService;
use app\wxwork\service\CoreService;
use app\wxwork\service\WxUserService;
use GuzzleHttp\Client;
use library\redisKeys;
use library\Sms;
use library\tool;
use library\wxwork\WXBizMsgCrypt;
use support\Log;
use support\Redis;
use support\Request;
use Webman\RedisQueue\Redis as RedisQueue;

class HomeController
{

    //验证应用回调消息
    public function callback(Request $request, $id)
    {
        $data = $request->all();
        Log::info('callback');
        Log::info(var_export($data, true));
        $info = WxworkConfig::getOne($id);
        if (empty($info)) {
            return json('');
        }

        //查询配置信息
        $encodingAesKey = $info['encodingAESKey'];
        $token = $info['token'];
        $corpId = $info['corpId'];
        $sVerifyMsgSig = $data['msg_signature'];
        $sVerifyTimeStamp = $data['timestamp'];
        $sVerifyNonce = $data['nonce'];
        $sVerifyEchoStr = $data['echostr'];
        // 需要返回的明文
        $sEchoStr = "";
        Log::info('callback--1');
        $wxcpt = new WXBizMsgCrypt($token, $encodingAesKey, $corpId);
        $errCode = $wxcpt->VerifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);
        Log::info('callback--2');

        if ($errCode == 0) {
            //echo $sEchoStr . "\n";
            // 验证URL成功，将sEchoStr返回
            return response($sEchoStr);
        } else {
            print("ERR: " . $errCode . "\n\n");
            Log::info('校验不通过=' . $errCode);
        }
    }

    //接收用户消息
    public function receiveMsg(Request $request, $id)
    {

        $data = $request->all();
        $sReqData = $request->rawBody();
        //查询配置信息
        $info = WxworkConfig::getOne($id);
        if (empty($info)) {
            return json('');
        }
        $encodingAesKey = $info['encodingAESKey'];
        $token = $info['token'];
        $corpId = $info['corpId'];
        $sReqMsgSig = $data['msg_signature'];
        $sReqTimeStamp = $data['timestamp'];
        $sReqNonce = $data['nonce'];
        $wxcpt = new WXBizMsgCrypt($token, $encodingAesKey, $corpId);
        $sMsg = "";  // 解析之后的明文
        $errCode = $wxcpt->DecryptMsg($sReqMsgSig, $sReqTimeStamp, $sReqNonce, $sReqData, $sMsg);
        if ($errCode != 0) {
            Log::error('解密企业用户消息失败' . $errCode);
            Log::info(var_export($data, true));
            return response();
        }
        Log::info('解密后内容：' . $sMsg);
        Log::info($sMsg);
        // 解密成功，sMsg即为xml格式的明文
        $xml = simplexml_load_string($sMsg, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (empty($xml->ToUserName)) {
            return response();
        }
        $corpId = $xml->ToUserName; //企业id
        $senderId = $xml->FromUserName->__toString(); //用户id
        $agentID = $xml->AgentID; //应用id
        $msgType = $xml->MsgType;

        //语音消息
        switch ($msgType) {
            case CoreService::MSG_TYPE_TEXT:
                //文本消息
                $content = trim($xml->Content->__toString() ?? '');
                break;
            case CoreService::MSG_TYPE_VOICE:
                //语音消息
                $mediaId = $xml->MediaId;
                $format = $xml->Format->__toString();
                $content = WxUserService::transVoiceText($id, $mediaId, $format);
                break;
            case CoreService::MSG_TYPE_IMAGE:
                //图片消息
                $mediaId = $xml->MediaId;
                $info = WxUserService::getMediaInfo($id, $mediaId);
                if (!empty($info['url'])) {
                    $content = $info['url'];
                }
                break;
            case CoreService::MSG_TYPE_VIDEO:
                //视频消息
                $mediaId = $xml->MediaId;
                $info = WxUserService::getMediaInfo($id, $mediaId);
                if (!empty($info['url'])) {
                    $content = $info['url'];
                }
                break;
            default:
                return response();
        }
        if (empty($content)) {
            return response();
        }
        $info = WxUserService::regis($id, $senderId);
        if (empty($info['user_id'])) {
            //处理手机号消息
            if (is_numeric($content) && strlen($content) == 11) {
                //绑定手机号
                //$resp = UserService::bindUser($corpId, $content, $senderId);
                $resp = "好的，已向 " . $content . " 发送6位验证码，请查收并输入。";
                $key = redisKeys::USER_MOBILE_CODE . $content;
                $code = rand(100000, 999999);
                //发送验证码
                Sms::sendSms($content, $code);
                Redis::setEx($key, 600, $code);
                UserWxwork::editByThirdId($id, $senderId, ['mobile' => $content, 'status' => UserWxwork::STATUS_SEND]);
                //回复消息
                $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $resp . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
                $sEncryptMsg = ""; //xml格式的密文
                $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
                if ($errCode == 0) {
                    return response($sEncryptMsg);
                } else {
                    Log::error('加密返回消息失败' . $errCode);
                    Log::error('msg=' . $sRespData);
                    return response();
                }
            }
            //验证手机号验证码
            if (!empty($info['status']) && $info['status'] == UserWxwork::STATUS_SEND) {
                $code = trim($content);
                if (!is_numeric($code) || strlen($code) != 6) {
                    $msg = "验证码错误";
                    $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $msg . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
                    $sEncryptMsg = ""; //xml格式的密文
                    $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
                    return response($sEncryptMsg);
                }
                $cacheCode = Redis::get(redisKeys::USER_MOBILE_CODE . $info['mobile']);
                if (empty($cacheCode) || $cacheCode != $code) {
                    $msg = "验证码错误";
                    $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $msg . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
                    $sEncryptMsg = ""; //xml格式的密文
                    $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
                    return response($sEncryptMsg);
                } else {
                    $msg = "验证成功！🎉 欢迎加入！

您好，欢迎使用AI销售助理Ivy！😊
可以帮你智能管理客户、商机跟进、日程提醒，还有话术建议和数据分析功能哦 😊";
                    $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $msg . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
                    $sEncryptMsg = ""; //xml格式的密文
                    $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);

                    UserWxwork::editByThirdId($id, $senderId, ['status' => UserWxwork::STATUS_SUCCESS]);
                    WxUserService::doRegis($info);
                    return response($sEncryptMsg);
                }
            }
            //回复消息
            $msg = "你好！我是你的AI销售助理Ivy 👋

可以帮你智能管理客户、商机跟进、日程提醒，还有话术建议和数据分析功能哦 😊

请输入手机号，我会发验证码给你。";
            $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $msg . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
            $sEncryptMsg = ""; //xml格式的密文
            $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
            if ($errCode == 0) {
                return response($sEncryptMsg);
            } else {
                Log::error('加密返回消息失败' . $errCode);
                Log::error('msg=' . $sRespData);
                return response();
            }
        }

        //手机号验证通过-正常处理用户消息
        $userId = $info['user_id'];
        $sessionId = WxUserService::getSessionId($userId);

        if (in_array($content, ['清空上下文', '清理上下文'])) {
            MainAgentSessions::delByUserId($userId);
            return response();
        }

        UserChat::addOne([
            'user_id' => $userId,
            'content' => $content,
            'is_robot' => 0,
        ]);
        $taskId = tool::uuid();  //taskId

        //向ai agent发起请求
        $res = AiTaskService::postTask($userId, $sessionId, $content, $taskId);
        if ($res == false) {
            return json(['content' => '网络异常，请稍后重试']);
        }
        //异步job处理返回: 避免超过5s
        RedisQueue::send('ai-task-wxwork', [
                'app_id' => $id,
                'task_id' => $taskId,
                'user_id' => $userId,
                'user_wxid' => $senderId,
                'query' => $content
            ]
        );
        $userInfo = User::getById($userId, ['profile']);
        if (empty($userInfo['profile'])) {
            //用户初始化agent,不需要回复，正在处理中
            return response();
        }
        //回复 - 正在处理中
        $link = LinkService::genShortLink($userId, LinkService::genTaskResult($taskId));
        $msg = "⏳ 正在处理中，查看详情↓↓
{$link}";
        $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $msg . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
        $sEncryptMsg = ""; //xml格式的密文
        $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
        if ($errCode == 0) {
            return response($sEncryptMsg);
        }
        return response();
    }

}
