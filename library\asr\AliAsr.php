<?php

namespace library\asr;

use AlibabaCloud\Client\AlibabaCloud;
use Exception;
use Webman\Exception\BusinessException;

class AliAsr
{
    protected $id;
    protected $key;
    protected $region;
    
    public function __construct()
    {
        $this->id = config('upload.alioss.accessKeyId');
        $this->key = config('upload.alioss.accessKeySecret');
        $this->region = 'cn-shanghai';//config('upload.alioss.region');
        AlibabaCloud::accessKeyClient($this->id, $this->key)
            ->regionId($this->region)
            ->asDefaultClient();
    }

    public function getToken()
    {
        try {
            $response = AlibabaCloud::nlsCloudMeta()
                                    ->v20180518()
                                    ->createToken()
                                    ->request();
            print $response . "\n";
            $token = $response["Token"] ?? [];
            if (empty($token)) {
                throw new BusinessException("token 获取失败");
            }
        } catch (Exception $exception) {
            throw new BusinessException($exception->getMessage());
        }
        return ['token' => $token['Id'], 'expire_time' => $token['ExpireTime']];
    }
}