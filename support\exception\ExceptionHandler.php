<?php

namespace support\exception;

use GuzzleHttp\Exception\GuzzleException;
use library\Mail;
use library\WechatNotice;
use library\WxRobotNotice;
use Respect\Validation\Exceptions\ValidationException;
use support\Log;
use think\exception\ValidateException;
use Throwable;
use Webman\Exception\ExceptionHandler as BaseExceptionHandler;
use Webman\Http\Request;
use Webman\Http\Response;
use Webman\Exception\BusinessException;
use support\exception\PageNotFoundException;


/**
 * Class Handler
 */
class ExceptionHandler extends BaseExceptionHandler
{
    public $dontReport = [
        BusinessException::class,
        ValidateException::class,
        ValidationException::class,
        GuzzleException::class,
        PageNotFoundException::class
    ];

    public function report(Throwable $exception)
    {
        if ($this->shouldntReport($exception)) {
            return;
        }
        $logs = '';
        if ($request = \request()) {
            $logs = $request->getRealIp() . ' ' . $request->method() . ' ' . trim($request->fullUrl(), '/');
        }
        $this->logger->error($logs . PHP_EOL . $exception);
        // 打印异常类型
        Log::error('Exception Type: ' . get_class($exception));
        if (env('APP_ENV') != 'dev') {
            WxRobotNotice::sendError("php报错：" . $exception->getMessage(), $exception->getTraceAsString());
        }
        //Mail::sendErrorNotice("php报错：" . substr($exception->getMessage(), 0, 30), $exception->getTraceAsString());
    }

    public function render(Request $request, Throwable $exception): Response
    {
        $request->setHeader('accept', 'application/json');
        if ($exception instanceof BusinessException || $exception instanceof GuzzleException) {
            $exception = new BusinessException($exception->getMessage(), $exception->getCode() ?: 1);
        } elseif ($exception instanceof ValidateException || $exception instanceof ValidationException) {
            $exception = new BusinessException($exception->getMessage(), 400);
        }
        return parent::render($request, $exception);
    }

}