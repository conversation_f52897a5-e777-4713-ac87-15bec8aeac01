<?php

namespace app\pyapi\controller;


use app\pyapi\service\LinkService;
use Respect\Validation\Validator as v;
use support\Request;

class LinkController extends BaseController
{


    public function getLinks(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
            'params' => v::notEmpty()->setName('链接类型'),
        ]);

        $result = [];
        foreach ($data['params'] as $v) {
            $res = LinkService::genLink($data['user_id'], $v['type'], $v['third_id'] ?? '');
            $result[] = [
                'type' => $v['type'],
                'third_id' => $v['third_id'] ?? '',
                'title' => $res['title'] ?? '',
                'link' => $res['link'] ?? '',
            ];
        }
        return $this->success($result);
    }

    //获取连接
    public function getLink(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
            'types' => v::notEmpty()->setName('链接类型'),
        ]);
        $thirdId = $request->post('third_id');
        $res = LinkService::genLink($data['user_id'], $data['types'], $thirdId);
        return $this->success(['title' => $res['title'] ?? '', 'link' => $res['link'] ?? '']);
    }

}