<?php

namespace library\ai;

use GuzzleHttp\Client as Client;
use support\Log;

class openai
{

    const API_URL = "https://openrouter.ai/api/v1/chat/completions";
    const TOKEN = "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";
    const MODEL_GPT41 = "openai/gpt-4.1";
    const MODEL_GPT41_MINI = "openai/gpt-4.1-mini";

    public static function send($message, $model, $temp = '', $responseFormat = '')
    {

        $data = [
            'stream' => false,
            'model' => $model,
            'temperature' => $temp ? 0.7 : $temp,  //随机因子:0表示固定结果
            'messages' => $message,
        ];
        if ($responseFormat == 'json') {
            $data['response_format'] = ['type' => 'json_object'];
        } elseif (is_array($responseFormat)) {
            $data['response_format'] = ['type' => 'json_schema', 'json_schema' => $responseFormat];
        }
        $res = static::requestSync($data);
        $res = json_decode($res, true);
        return $res;
    }

    //stream
    public static function sendStream($message, $param, $temp = 0, $model = '')
    {
        $data = [
            'stream' => true,
            'model' => $model,
            'temperature' => $temp,  //随机因子:0表示固定结果
            'messages' => $message,
        ];
        static::requestStream($data, $param);
        return true;
    }

    //send 带函数
    public static function sendByFunction($messages, $functions, $model = '')
    {
        $data = [
            'stream' => false,
            'model' => $model,
            'temperature' => 0.1,  //随机因子:0表示固定结果
            'messages' => $messages,
            'tools' => $functions,
        ];
        $res = static::requestSync($data);
        $res = json_decode($res, true);
        if (empty($res)) {
            return "";
        }
        if (empty($res['choices'])) {
            return '';
        }
        return $res;
    }

    //requestStream
    public static function requestStream($messages, $param)
    {
        $callback = new openaiStreamHandler($param);
        $data = json_encode($messages);
        $headers = array(
            "Content-Type: application/json",
            "Authorization: Bearer " . static::TOKEN,
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, static::API_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, [$callback, 'callback']);
        curl_setopt($ch, CURLOPT_TIMEOUT, 1200);//超时时间
        $res = curl_exec($ch);
        Log::info(' model=' . $messages['model'] . '  res=' . $res);
        if (curl_errno($ch)) {
            Log::error(curl_error($ch) . PHP_EOL);
        }
        curl_close($ch);
    }

    public static function requestSync($data)
    {
        Log::info('request llm ' . $data['model']);
        if (count($data['messages']) > 0) {
            Log::info('request llm message= ' . $data['messages'][count($data['messages']) - 1]['content']);
        }
        $data = json_encode($data);
        $headers = array(
            "Content-Type: application/json",
            "Authorization: Bearer " . static::TOKEN,
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, static::API_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        //curl_setopt($ch, CURLOPT_WRITEFUNCTION, [$callback, 'callback']);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);//超时时间
        $res = curl_exec($ch);
        Log::info('request llm res=' . trim($res));
        if (curl_errno($ch)) {
            Log::error(curl_error($ch) . PHP_EOL);
        }
        curl_close($ch);
        return $res;
    }

}
