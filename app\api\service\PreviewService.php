<?php

namespace app\api\service;

use library\AliImm;
use library\redisKeys;
use library\tool;
use support\Log;
use support\Redis;

class PreviewService
{

    public static function getToken($fileName, $fileUrl, $isReadOnly)
    {
        ini_set('memory_limit', '600M');
        //redis 缓存token 30分钟失效 重复使用 @todo
        $key = redisKeys::ALIYUN_FILE_PREVIEW_TOKEN . md5($fileUrl);
        if (Redis::exists($key)) {
            $tokenInfo = Redis::get($key);
            return json_decode($tokenInfo, true);
        }
        $config = [
            'oss_access_key' => 'LTAItwkMZEO7KDat',
            'oss_access_secret' => 'HtUiTgT28itPs5xfokxgtlhkcufHa6',
            'imm_endpoint' => 'imm.cn-shenzhen.aliyuncs.com',
            'imm_pro_name' => 'ivy',
            'oss_bucket' => 'sale-agent',
        ];
        //文件名后缀要小写
        $fileName = tool::fileExtToLower($fileName);
        $tokenInfo = AliImm::getToken($config, $fileName, $fileUrl, $isReadOnly);
        if (!empty($tokenInfo['token'])) {
            Redis::setex($key, 1800, json_encode($tokenInfo));
        }
        return $tokenInfo;
    }

}