<?php

namespace app\wechat\service;

use app\model\TaskResult;
use app\model\UserChat;
use app\wxwork\service\CoreService;
use library\ai\qwen;
use library\redisKeys;
use support\Log;
use support\Redis;

class AiTaskService
{

    public static function summary($userId, $userWxid, $taskId, $query, $resContent)
    {
        WebsocketService::push([
                'userId' => $userWxid,
                'content' => $resContent,
            ]
        );
        UserChat::addOne([
            'user_id' => $userId,
            'content' => $resContent,
            'is_robot' => 1,
        ]);
    }

    public static function finished($userId, $userWxid, $taskId, $resContent)
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        Redis::rPush($key, json_encode([
            'content' => "",
            'event' => 'allCompleted',
            'content_type' => 'str',
        ]));
        //给用户推送最后的总结结果
        Log::info('给用户' . $userWxid . '推送结果：' . $resContent);

        //CoreService::sendMsg($appid, $userWxid, $resContent, CoreService::MSG_TYPE_TEXT);
        WebsocketService::push([
                'userId' => $userWxid,
                'content' => $resContent,
            ]
        );

        UserChat::addOne([
            'user_id' => $userId,
            'content' => $resContent,
            'is_robot' => 1,
        ]);
        static::saveResult($userId, $taskId);
    }

    //
    public static function saveResult($uid, $taskId)
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        $list = Redis::lRange($key, 0, -1);
        if (empty($list)) {
            return false;
        }
        $jsonStr = "[";
        foreach ($list as $k => $v) {
            if ($k == 0) {
                $jsonStr .= $v;
            } else {
                $jsonStr .= "," . $v;
            }
        }
        $jsonStr .= "]";
        TaskResult::addOne([
            'user_id' => $uid,
            'task_id' => $taskId,
            'result' => $jsonStr,
        ]);
    }
}