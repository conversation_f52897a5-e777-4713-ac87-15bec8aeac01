<?php

namespace app\pyapi\controller;


use app\model\CustomerInteractions;
use app\model\Customers;
use app\pyapi\service\LinkService;
use Respect\Validation\Validator as v;
use support\Log;
use support\Request;
use Webman\Exception\BusinessException;

class CustomerController
{
    //add
    public function add(Request $request)
    {
        $data = v::input($request->all(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
            'data_list' => v::notEmpty()->setName('客户数据'),
        ]);
        $userId = $request->input('user_id');
        $dataList = $data['data_list'];
        $successList = $failList = [];
        $successCnt = 0;
        foreach ($dataList as $data) {
            try {
                $data['user_id'] = $userId;
                $data['company_short_name'] = $data['company_name'] ?? ''; //默认录入简称
                $data['company_name'] = $data['company_full_name'] ?? '';
                $data['contact_name'] = $data['contact_name'] ?? '';
                if (empty($data['company_short_name']) && empty($data['contact_name'])) {
                    throw new BusinessException('添加失败，客户名称或联系人不能都为空');
                }
                $data['customer_level'] = trim($data['customer_level'] ?? ''); //客户级别
                $data['customer_level'] = Customers::LEVEL_VALUES[$data['customer_level']] ?? Customers::LEVEL_PRE;
                //$id = Customers::insertGetId($data);
                // $id = Customers::addOne($data);
                // $link = LinkService::genShortLink($data['user_id'], LinkService::genCustomerDetail($id));
                $successList[] = [
                    'data' => [
                        // 'customer_id' => $id,
                        'company_name' => $data['company_short_name'],
                        'contact_name' => $data['contact_name'],
                    ],
                    // 'link' => $link,
                ];
                $successCnt++;
            } catch (\Exception $e) {
                Log::error('添加客户失败：' . $e->getMessage() . ' data=' . var_export($data, true));
                $failList[] = [
                    'data' => $data,
                    'msg' => $e->getMessage(),
                ];
            }
        }
        $returnData = [
            'code' => 0,
            'msg' => '客户添加已完成',
            'success_list' => $successList,
            'fail_list' => $failList,
        ];
        if ($successCnt > 10) {
            foreach ($returnData['success_list'] as &$item) {
                unset($item['link']);
            }
            $returnData['customer_list_link'] = LinkService::genShortLink($userId, LinkService::genCustomerList());
        }
        return json($returnData);
    }

    //get
    public function detail(Request $request)
    {
        $data = v::input($request->get(), [
            'customer_id' => v::notEmpty()->setName('客户ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $data['customer_id'] = explode(',', str_replace('，', ',', $data['customer_id']));
        //$info = Customers::getOne($data['user_id'], $data['customer_id']);
        $list = Customers::getListByIds($data['customer_id'], $data['user_id']);
        if (empty($list)) {
            return json(['code' => 1, 'msg' => '客户信息不存在', 'data' => [
                'customer_id' => $data['customer_id'],
            ]]);
        }
        foreach ($list as $k => $v) {
            $link = LinkService::genShortLink($data['user_id'], LinkService::genCustomerDetail($v['customer_id']));
            $list[$k]['link'] = '点击查看详情：' . $link;
            unset($list[$k]['deleted_at'], $list[$k]['sort_no']);
        }
        return json(['code' => 0, 'msg' => 'ok', 'data' => $list]);
    }

    //list
    public function list(Request $request)
    {
        $data = v::input($request->all(), [
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        $ids = $request->input('ids', []);
        $query = Customers::getUserQuery($data['user_id'], ['customer_id', 'company_short_name', 'contact_name', 'customer_level', 'phone', 'created_at']);
        if (!empty($ids)) {
            $query->whereIn('customer_id', $ids);
        }
        $list = $query->get()->toArray();
        foreach ($list as $k => $v) {
            $list[$k]['customer_level'] = Customers::LEVEL_LABELS[$v['customer_level']] ?? '';
            $list[$k]['company_name'] = $v['company_short_name'];
            $list[$k]['created_at'] = date('Y-m-d', strtotime($v['created_at']));
            unset($list[$k]['company_short_name']);
        }
        $link = LinkService::genShortLink($data['user_id'], LinkService::genCustomerList());
        return json(['code' => 0, 'msg' => 'ok', 'total' => count($list), 'data' => $list, 'link' => '点击查看我的客户列表：' . $link]);
    }

    //del
    public function del(Request $request)
    {
        $data = v::input($request->post(), [
            'customer_id' => v::notEmpty()->setName('客户ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        //是否存在
//        $info = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
//        if (empty($info)) {
//            return json(['code' => 1, 'msg' => '客户信息不存在', 'data' => [
//                'customer_id' => $data['customer_id'],
//            ]]);
//        }
        $data['customer_id'] = explode(',', str_replace('，', ',', $data['customer_id']));
        Customers::del($data['user_id'], $data['customer_id']);
        return json(['code' => 0, 'msg' => '删除客户成功']);
    }

    //edit
    public function edit(Request $request)
    {
        $data = v::input($request->post(), [
            'customer_id' => v::notEmpty()->setName('客户ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        //是否存在
        $info = Customers::getOne($data['user_id'], $data['customer_id']);
        if (empty($info)) {
            return json(['code' => 1, 'msg' => '客户信息不存在', 'data' => [
                'customer_id' => $data['customer_id'],
            ]]);
        }
        $updates = array_filter($request->post());
        $updates['id'] = $data['customer_id']; //添加一个映射字段 -> saveData
        //公司简称
        if ($request->post('company_name')) {
            $updates['company_short_name'] = trim($request->post('company_name'));
        }
        //公司全称
        if ($request->post('company_full_name')) {
            $updates['company_name'] = trim($request->post('company_full_name'));
        }
        if ($request->post('contact_name')) {
            $updates['contact_name'] = trim($request->post('contact_name'));
        }
        if ($request->post('contact_title')) {
            $updates['contact_title'] = trim($request->post('contact_title'));
        }
        if ($request->post('customer_level')) {
            $updates['customer_level'] = trim($request->post('customer_level'));
            $updates['customer_level'] = Customers::LEVEL_VALUES[$updates['customer_level']] ?? Customers::LEVEL_PRE;
        }
        if ($request->post('phone')) {
            $updates['phone'] = trim($request->post('phone'));
        }
        if ($request->post('contact_remark')) {
            $updates['contact_remark'] = trim($request->post('contact_remark'));
        }
        if ($request->post('notes')) {
            $updates['notes'] = trim($request->post('notes'));
        }
        if (!empty($updates)) {
            $info->saveData($updates);
            // Customers::where("user_id", $data['user_id'])->where('customer_id', $data['customer_id'])->update($updates);
            $link = LinkService::genShortLink($data['user_id'], LinkService::genCustomerDetail($data['customer_id']));
            return json(['code' => 0, 'msg' => '更新客户信息成功', 'data' => $updates, 'link' => $link]);
        }

        return json(['code' => 0, 'msg' => '没有字段信息要更新']);

    }

    //添加客户互动记录
    public function addInteract(Request $request)
    {
        $data = v::input($request->post(), [
            'customer_id' => v::notEmpty()->setName('客户ID'),
            'user_id' => v::notEmpty()->setName('用户ID'),
            'content' => v::stringType()->setName('互动内容'),
        ]);
        $data['interaction_type'] = $request->post('interaction_type', '');
        $data['next_action'] = $request->post('next_action', '');
        $customerInfo = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
        if (empty($customerInfo)) {
            return json(['code' => 1, 'msg' => '客户信息不存在']);
        }
        $id = CustomerInteractions::addOne($data);
        return json(['code' => 0, 'msg' => '添加客户互动记录成功', 'data' => [
            'interaction_id' => $id,
            'customer_id' => $data['customer_id'],
            'content' => $data['content'],
        ]]);
    }

    // get 客户互动记录列表
    public function listInteract(Request $request)
    {
        $data = v::input($request->post(), [
            'customer_id' => v::number()->setName('客户ID'),
            'user_id' => v::number()->setName('用户ID'),
        ]);
        $customerInfo = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
        if (empty($customerInfo)) {
            return json(['code' => 1, 'msg' => '客户信息不存在']);
        }
        $list = CustomerInteractions::list($data['user_id'], $data['customer_id']);
        return json(['code' => 0, 'msg' => 'get客户互动列表成功', 'total' => count($list), 'data' => $list]);
    }

    // 排序
    public function sortAll(Request $request)
    {
        $data = v::input($request->post(), [
            'ids' => v::notEmpty()->setName('ids'),
            'user_id' => v::notEmpty()->setName('用户ID'),
        ]);
        Customers::sortAll($data['user_id'], $data['ids']);
        return json(['code' => 0, 'msg' => '排序成功']);
    }

}