<?php

namespace app\wxwork\service;

use app\model\TaskResult;
use app\model\User;
use app\model\UserChat;
use app\wechat\service\WebsocketService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use library\ai\qwen;
use library\redisKeys;
use support\Log;
use support\Redis;

class AiTaskService
{

    public static function summary($appid, $userId, $userWxid, $taskId, $query, $resContent)
    {
        //增加一个调用工具的步骤
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        Redis::rPush($key, json_encode([
            'content' => '总结',
            'event' => 'ToolCallStarted',
            'content_type' => 'str',
            "member_name" => '总结',
        ]));
        //ai总结
        $messages = [
            [
                'role' => 'system',
                'content' => '润色一下：
1. 用专业轻松的语气，像朋友聊天一样自然
2. 直接返回结果
3. 用Emoji适当点缀但不过度
4. 重要信息独立成句或者成段，段落之间接可以隔一行
4. 如有链接，可以直接给链接，不要有任何对链接的解释说明（点击、查看、详情之类），不要用markdown格式,没有的话请忽略
5. 输出内容保持信息精简,用中文输出.'
            ],
            [
                'role' => 'user',
                'content' => $resContent
            ]
        ];
        $res = qwen::send($messages, qwen::MODEL_MAX, 0.7);
        if (!empty($res) && !empty($res['choices'])) {
            $summ = $res['choices'][0]['message']['content'];
            Log::info('AI总结结果：' . $summ);
            Redis::rPush($key, json_encode([
                'content' => "总结",
                'event' => 'ToolCallCompleted',
                'content_type' => 'str',
                "member_name" => '总结',
            ]));
            Redis::rPush($key, json_encode([
                'content' => $summ,
                'event' => 'summary',
                'content_type' => 'str',
            ]));
            Redis::rPush($key, json_encode([
                'content' => "",
                'event' => 'allCompleted',
                'content_type' => 'str',
            ]));
            //给用户推送最后的总结结果
            CoreService::sendMsg($appid, $userWxid, $summ, CoreService::MSG_TYPE_TEXT);
            UserChat::addOne([
                'user_id' => $userId,
                'content' => $summ,
                'is_robot' => 1,
            ]);
            static::saveResult($userId, $taskId);
        }

    }

    //
    public static function finished($appid, $userId, $userWxid, $taskId, $resContent)
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        Redis::rPush($key, json_encode([
            'content' => "",
            'event' => 'allCompleted',
            'content_type' => 'str',
        ]));
        //给用户推送最后的总结结果
        Log::info('给用户' . $userWxid . '推送结果：' . $resContent);
        CoreService::sendMsg($appid, $userWxid, $resContent, CoreService::MSG_TYPE_TEXT);
        UserChat::addOne([
            'user_id' => $userId,
            'content' => $resContent,
            'is_robot' => 1,
        ]);
        //@todo 判断如果是初始化完毕，再推送一条信息
        static::saveResult($userId, $taskId);
    }

    //
    public static function saveResult($uid, $taskId)
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        $list = Redis::lRange($key, 0, -1);
        if (empty($list)) {
            return false;
        }
        $jsonStr = "[";
        foreach ($list as $k => $v) {
            if ($k == 0) {
                $jsonStr .= $v;
            } else {
                $jsonStr .= "," . $v;
            }
        }
        $jsonStr .= "]";
        TaskResult::addOne([
            'user_id' => $uid,
            'task_id' => $taskId,
            'result' => $jsonStr,
        ]);
    }

    //向ai-agent 发送task请求
    public static function postTask($userId, $sessionId, $query, $taskId)
    {
        try {
            //判断用户信息是否初始化
            $userInfo = User::getById($userId);
            $agentId = '001';
            $userStr = "";

//            if (empty($userInfo['profile'])) {
//                $agentId = '002'; // get 用户画像agent
//                $userStr = "";
//            } else {
//                $agentId = '001'; //主agent
//            }
            if ($userInfo) {
                $userStr = "name:  {$userInfo['name']}
mobile: {$userInfo['mobile']}
company: {$userInfo['company']}
行业: {$userInfo['business_type']}
用户描述: {$userInfo['profile']}";
            }

            $client = new Client();
            return $client->request('POST', 'http://' . env('AI_AGENT_SERVER') . '/task', [
                'json' => [
                    'query' => $query,
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'task_id' => $taskId,
                    'agent_id' => $agentId,
                    'user_info' => $userStr,
                ]
            ]);
        } catch (GuzzleException $e) {
            Log::error('向ai-agent服务发送请求失败：' . $e->getMessage());
            return false;
        }
    }

}