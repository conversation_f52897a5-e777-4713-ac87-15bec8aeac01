<?php

namespace app\pyapi\service;

use app\model\Attachment;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use app\model\UserChat;
use app\wxwork\service\NoticeService;
use GuzzleHttp\Client;
use library\ai\qwen;
use library\Alioss;
use library\tool;
use support\Db;
use support\Log;
use Webman\Exception\BusinessException;

class BusiOppService
{
    public static function aiGetStageAndSuggest($boId, $userId, $appendMessages = [])
    {
        $messages = array_merge(UserChat::getMessages($userId, 10), $appendMessages);
        !is_array($boId) && $boId = [$boId];
        $detail = json_encode(BusinessOpportunities::getUserQuery($userId, ['title', 'content', 'source', 'status', 'budget', 'notes'])->whereIn('bo_id', $boId)->get()->toArray(), JSON_UNESCAPED_UNICODE);
        $todoList = json_encode(TodoItems::getQuery($userId, ['bo_id', 'title', 'description', 'due_date', 'is_finished'])->whereIn('bo_id', $boId)->get()->toArray(), JSON_UNESCAPED_UNICODE);
        // 请求AI判断阶段
        $prompt = <<<EOT
        您是一个CRM AI智能体中的销售阶段评估工具，目标是分析商机当前销售阶段，自动判断阶段目标完成状态，并提供具体行动建议。基于以下框架分析：

        # 销售阶段及目标：
        1. 初步接触
        - 阶段定义：仅与客户建立初步联系，未明确具体需求
        - ["id" => 1, "title" => "明确客户的真实需求和当前问题", "description" => "了解客户面临的具体问题、痛点，以及需求是否明确具体。若客户表达模糊或仅为随意交流，需警惕后续成交可能性低"]
        - ["id" => 2, "title" => "找到并建立与关键联系人的沟通", "description" => "识别能影响决策的关键人并建立有效沟通。若联系人频繁更换或沟通不畅，说明项目推进难度大，需调整策略"]

        2. 需求确认
        - 阶段定义：客户有实际需求，正在了解需求细节、预算和决策流程，未进入竞品对比
        - ["id" => 3, "title" => "明确客户的具体需求和应用场景", "description" => "让客户把需求和使用场景说清楚，需求越详细越好。若客户说不清楚，要主动引导，避免后期反复修改影响进度"]
        - ["id" => 4, "title" => "搞清楚客户的预算和审批流程", "description" => "了解客户大概预算和谁能拍板、流程怎么走。若客户不愿透露预算或流程，说明项目优先级不高，要注意投入产出比"]

        3. 竞品分析
        - 阶段定义：客户在对比多家方案，评估优劣，未进入合同谈判
        - ["id" => 5, "title" => "了解客户在对比哪些方案", "description" => "知道客户还在看哪些竞品、关注点是什么。如果客户只是随便看看，采购意愿不强，要判断是否值得继续投入精力"]
        - ["id" => 6, "title" => "明确我们相对竞品的优势", "description" => "搞清楚客户对竞品和我们的看法，突出我们的优势。如果客户只关心价格或明显偏向其他家，要警惕被“陪标”或只是被用来压价"]

        4. 商务谈判
        - 阶段定义：客户认可方案，正在走招采流程、谈判合同、价格等，尚未签约
        - ["id" => 7, "title" => "明确客户的采购时间表和流程", "description" => "知道客户什么时候能定下来、每一步怎么走。如果客户迟迟不给时间表或流程，项目可能会拖延或被搁置，要及时调整跟进方式"]
        - ["id" => 8, "title" => "搞清楚合同和付款的关键条款", "了解合同签署、付款方式和主要商务条款。如果客户反复修改合同或拖延签约，说明内部还没统一意见，要多和关键决策人沟通，避免时间浪费"]

        5. 赢单关闭
        - 阶段定义：合同已签署，项目已实施、交付或验收
        - ["id" => 9, "title" => "完成合同签署和项目交接", "description" => "确保合同已签署、相关资料和信息已交接给实施或服务团队，及时跟进客户后续需求，维护客户关系，为二次销售或转介绍做准备"]
        - ["id" => 10, "title" => "总结本次销售过程的经验和不足", "description" => "复盘整个销售流程，记录成功经验和遇到的问题，优化后续销售策略，提升个人能力"]

        6. 丢单关闭
        - 阶段定义：客户已选其他供应商、项目终止或长期无回应
        - ["id" => 11, "title" => "明确丢单原因并记录", "description" => "了解客户未选择我们的真实原因（如价格、产品、时机等），详细记录，便于后续分析和改进"]
        - ["id" => 12, "title" => "维护好客户关系，争取后续机会", "description" => "即使丢单也要保持良好沟通，表达感谢，争取未来有合作机会或客户转介绍，避免因丢单影响个人和公司口碑"]

        # 商机信息：
        - 商机详情：$detail
        - 商机活动：$todoList

        # 任务：
        1. 分析阶段：
        - 严格对照每个阶段的“阶段定义”，判断当前阶段及目标完成状态（已完成/未完成）
        - 严格依据阶段定义判断当前阶段。
        - 如出现“已签约”、“已实施”、“已交付”、“项目验收”等，直接判定为赢单关闭。
        - 如出现“客户已选其他供应商”、“项目终止”或“无回应超30天”，判定为丢单关闭，并在additional_notes中说明。
        - 如信息涉及多个阶段，优先以最靠后的阶段为准，并说明理由
        - 信息不足时，结合最近历史商机相关的待办活动进行推测，并说明依据


        2. 提供建议：
        - 针对未完成目标，给出3-5条具体行动建议，避免模板化
        - 每次输出至少2条风险及应对措施（如信息极少可只给1条）
        - 赢单/丢单阶段需包含后续关系维护建议

        # 输出格式（JSON）：
        ```json
        {
        "current_stage": "[阶段编号]",
        "stage_goals": [
            {
            "goal_id": "[目标id]",
            "is_finished": "[1/0]"
            },
            {
            "goal_id": "[目标id]",
            "is_finished": "[1/0]"
            }
        ],
        "action_recommendations": [
            "[建议1]",
            "[建议2]",
            "..."
        ],
        "risks_and_mitigations": [
            {
            "risk": "[风险]",
            "mitigation": "[应对]"
            }
        ],
        "additional_notes": "[假设或说明]"
        }
        ```

        # 示例输出：
        ```json
        {
        "current_stage": "2",
        "stage_goals": [
            {
            "goal_id": "3",
            "is_finished": "1"
            },
            {
            "goal_id": "4",
            "is_finished": "0"
            }
        ],
        "action_recommendations": [
            "安排会议深入探讨使用场景，用针对性问题引导客户提供细节",
            "间接询问预算，如‘这类项目的典型投资范围是多少？’",
            "确认关键决策人，询问审批流程涉及哪些人员",
            "提供案例展示类似场景的解决方案，激发客户表达需求"
        ],
        "risks_and_mitigations": [
            {
            "risk": "客户需求模糊，承诺度低",
            "mitigation": "设定提交详细需求的截止日期，测试参与度"
            },
            {
            "risk": "预算保密，项目优先级低",
            "mitigation": "提议小型试点项目，适应潜在预算限制"
            }
        ],
        "additional_notes": "因CRM未记录预算讨论，假设预算目标未完成"
        }
        ```

        # 约束：
        - 仅基于提供信息判断，不得捏造数据
        - 建议简洁、具体，贴合当前阶段
        - 如关键信息缺失，需在additional_notes中说明推理或假设
        EOT;
        $messages[] = ['role' => 'user', 'content' => $prompt];
        $result = qwen::send($messages, qwen::MODEL_MAX, 0.2, 'json');
        $message = $result['choices'][0]['message']['content'] ?? '';
        $data = json_decode($message, true);
        // var_dump($messages, $result);
        $data['guidance_text'] = '';
        if (!empty($data['action_recommendations'])) {
            $data['guidance_text'] .= "\n建议行动\n";
            foreach ($data['action_recommendations'] as $item) {
                $data['guidance_text'] .= "- {$item}\n";
            }
        }
        if (!empty($data['risks_and_mitigations'])) {
            $data['guidance_text'] .= "\n风险与应对措施\n";
            foreach ($data['risks_and_mitigations'] as $item) {
                $data['guidance_text'] .= "- 风险：{$item['risk']}\n  应对措施：{$item['mitigation']}\n";
            }
        }
        if (!empty($data['additional_notes'])) {
            $data['guidance_text'] .= "\n补充说明\n" . $data['additional_notes'];
        }
        return $data;
    }

    public static function followupRemind()
    {
        $time = time();
        // 重点客户7、15、30天提醒，普通客户15、30天提醒，其他客户30天提醒
        $notifyDays = [7 => Customers::LEVEL_IMPORTANT, 15 => Customers::LEVEL_NORMAL, 30 => null];
        foreach ($notifyDays as $day => $customerLevel) {
            $date = date('Y-m-d', $time - $day * 86400);
            $boIds = array_column(TodoItems::getBeforeLastByBusinessAndTodoDueDate([$date . ' 00:00:00', $date . ' 23:59:59'], fields: 'bo_id'), 'bo_id');
            $boList = $boIds ? BusinessOpportunities::whereIn('bo_id', $boIds)
                ->get(['bo_id', 'customer_id', 'title'])
                ->toArray() : [];
            $customerMapBoIds = [];
            $customerIds = [];
            $customerMapBoList = [];
            foreach ($boList as $item) {
                if (!isset($customerMapBoIds[$item['customer_id']])) {
                    $customerMapBoIds[$item['customer_id']] = [];
                    $customerIds[] = $item['customer_id'];
                    $customerMapBoList[$item['customer_id']] = [];
                }
                $customerMapBoIds[$item['customer_id']][] = $item['bo_id'];
                $customerMapBoList[$item['customer_id']][] = $item;
            }
            $customers = $customerIds ? Customers::whereIn('customer_id', $customerIds)
                ->get(['customer_id', 'user_id', 'company_short_name', 'contact_name', 'phone', 'customer_level'])
                ->toArray() : [];
            foreach ($customers as $customer) {
                if ($customerLevel && $customer['customer_level'] < $customerLevel) {
                    continue;
                }
                $customerBoIds = $customerMapBoIds[$customer['customer_id']];
                $customerBoList = $customerMapBoList[$customer['customer_id']];
                $pushBoListText = $boListText = '待跟进商机\n';
                foreach ($customerBoList as $bo) {
                    $link = LinkService::genBusiDetail($bo['bo_id']);
                    $boListText .= "- [{$bo['title']}]({$link})\n";
                    $pushBoListText .= "- {$bo['title']}\n";
                }
                $phoneText = $customer['phone'] ? "({$customer['phone']})" : '';
                $remindText = "⚠ {$customer['company_short_name']}的商机已有{$day}天未更新了。\n\n可以联系{$customer['contact_name']}{$phoneText}跟进一下\n\n";
                $guidanceText = self::aiGetStageAndSuggest($customerBoIds, $customer['user_id'], [['role' => 'assistant', 'content' => $remindText]])['guidance_text'];
                NoticeService::pushNotice($customer['user_id'], $remindText . $boListText . $guidanceText, addToChat: true, payload: ['url' => LinkService::genChat()], pushContent: $remindText . $pushBoListText . $guidanceText);
                Log::info("已推送客户id={$customer['customer_id']}的商机提醒");
            }
        }
    }
}