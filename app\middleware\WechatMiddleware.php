<?php

namespace app\middleware;

use app\wechat\service\UserService;
use library\redisKeys;
use ReflectionClass;
use support\Log;
use support\Redis;
use Webman\Exception\BusinessException;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

/*
 * 企微机器人使用 - 中间件
 */

class WechatMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        $token = $request->post('token');
        $userId = $request->post('user_wx_id');
        $userName = $request->post('user_name');
        $appId = $request->post('app_id');

        // 通过反射获取控制器哪些方法不需要登录
        $controller = new ReflectionClass($request->controller);
        $noNeedLogin = $controller->getDefaultProperties()['noNeedLogin'] ?? [];

        // 访问的方法需要登录
        if (!in_array($request->action, $noNeedLogin)) {
            // 拦截请求
            if (empty($token) || $token != 'ProFly@2023#0816Harris' || empty($userId)) {
                throw new BusinessException('环境异常', 401);
            }
//            $cache = Redis::get(redisKeys::WECHAT_USER_TOKEN . $userId);
//            if (empty($cache)) {
//                UserService::register($appId, $userId, $userName);
//                Redis::setEx(redisKeys::WECHAT_USER_TOKEN . $userId, 86400 * 7, $uid);
//            }

            //$request->setHeader(['user_wxid' => $userId]);
        }
        return $handler($request);
    }
}