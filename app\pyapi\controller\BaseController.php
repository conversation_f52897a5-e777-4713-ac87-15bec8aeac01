<?php

namespace app\pyapi\controller;

class BaseController
{
    protected function success($data = null, int $code = 0, string $msg = "ok"): \support\Response
    {
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

    protected function error(string $msg = 'fail', int $code = 1, $data = null): \support\Response
    {
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }
}