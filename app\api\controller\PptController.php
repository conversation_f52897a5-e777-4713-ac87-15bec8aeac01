<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\UserPpt;
use library\aippt\Api;
use Respect\Validation\Validator as v;
use support\Request;

class PptController extends BaseController
{

    public function token(Request $request)
    {
        $data = $request->all();
        v::input($data, [
            'ppt_id' => v::notEmpty()->setName('ppt_id'),
        ]);
        $userId = getUserId();
        $info = UserPpt::getOneByPptId($userId, $data['ppt_id'], ['id']);
        if (empty($info)) {
            return $this->error('PPT不存在');
        }
        $token = Api::getToken($userId);
        return $this->success(['token' => $token]);
    }

}