<?php

namespace app\api\controller;

use app\api\service\AttachmentService;
use app\api\service\PreviewService;
use app\controller\BaseController;
use app\model\Attachment;
use app\model\BaseModel;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use app\validate\AttachmentValidate;
use GuzzleHttp\Client;
use library\Alioss;
use library\tool;
use Respect\Validation\Validator as v;
use support\Db;
use support\Log;
use support\Request;
use Webman\Exception\BusinessException;

/**
 * 附件管理
 */
class AttachmentController extends BaseController
{
    protected $indexFields = ['id', 'parent_id', 'classify', 'classify_id', 'name', 'type', 'url', 'size', 'created_at', 'updated_at'];
    protected $indexSortFields = ['id', 'name'];
    protected $indexFilterFields = [
        'classify' => '=',
        'classify_id' => '=',
        'name' => 'like',
    ];
    protected $indexSort = [
        'id' => 'desc',
    ];
    protected $ossUtil;
    protected $service;

    public function __construct()
    {
        parent::__construct();
        $this->model = new Attachment();
        $this->validate = new AttachmentValidate;
        $this->ossUtil = new Alioss;
        $this->service = new \app\api\service\AttachmentService;
    }

    protected function beforeList(Request $request, \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder &$query)
    {
        $data = $request->all();
        Attachment::makeListQuery(getUserId(), $data, $query, $this->indexFields);
    }

    protected function afterList(Request $request, array &$data)
    {
        $params = $request->all();
        if (!empty($data['list'])) {
            $querySubClassify = in_array($params['classify'] ?? 0, [Attachment::CLASSIFY_CUSTOMER, Attachment::CLASSIFY_BUSINESS]);
            $rootParentId = 0;
            foreach ($data['list'] as $i => &$item) {
                if ($querySubClassify && $item['classify_id'] == $params['classify_id'] && $item['type'] == 'dir') {
                    $delIndex = $i; // 筛选客户和商机时不显示自己这一级的目录
                    $rootParentId = $item['id']; // 子目录提到根目录
                    break;
                }
            }
            if (isset($delIndex)) {
                unset($data['list'][$delIndex]);
            }
            if (empty($params['type']) || $params['type'] == 'dir') { // 只查文件不需要构建目录树
                $data['list'] = tool::buildTree($data['list'], rootParentId: $rootParentId);
            }
        }
    }

    protected function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        if (empty($data['id'])) { // 新增
            if (!empty($data['classify']) && !empty($data['classify_id']) && empty($data['parent_id'])) { // 找出该分类目录的id
                $classifyInfo = Attachment::getClassifyRootDir($data['classify'], $data['classify_id'], $data['user_id'], ['id']);
                if (empty($classifyInfo)) {
                    throw new BusinessException('该分类目录不存在');
                }
                $data['parent_id'] = $classifyInfo['id'];
            }
            if (empty($data['classify'])) {
                $data['classify'] = Attachment::CLASSIFY_TEMP; // 放入临时文件夹,防止聊天文件还没发送就被归档了
            }
        }
        Db::transaction(function () use ($model, $data) {
            if (empty($data['id'])) {
                $data['type'] = strtolower(pathinfo($data['url'], PATHINFO_EXTENSION)); // 自动获取文件类型
            }
            $oldData = $model->toArray();
            $model->fill($data)->save();
            $this->service->withMoveFile($oldData, $data);
            if (empty($data['id']) && $data['classify'] != Attachment::CLASSIFY_TEMP) {
                // 写入向量库
                $classify = $data['classify'] ?? 0;
                $this->service->vectorAdd($data['url'], $data['type'], $data['user_id'], $model->id, $data['name'], $classify == Attachment::CLASSIFY_PRODUCT);
            }
        });
        return true;
    }

    //预览文件
    public function previewToken(Request $request)
    {
        $data = $request->get();
        v::input($data, [
            'id' => v::notEmpty()->setName('文件id'),
        ]);
        $userId = getUserId();
        $info = Db::table('attachment')->where('user_id', $userId)->where('id', $data['id'])->select(['name', 'url', 'type'])->first();
        if (empty($info)) {
            return $this->error('文件不存在');
        }
        $tokenInfo = PreviewService::getToken($info->name, $info->url, true);
        return $this->success([
            'token' => $tokenInfo['token'] ?? '',
            'url' => $tokenInfo['url'] ?? '',
        ]);
    }

}