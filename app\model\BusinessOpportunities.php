<?php

namespace app\model;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use support\Db;
use support\Model;
use Webman\Exception\BusinessException;

class BusinessOpportunities extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'customer_id',
        'title',
        'content',
        'source',
        'status',
        'budget',
        'notes',
        'sort_no',
        'suggest_todo_finish_ids',
    ];
    protected $table = 'business_opportunities';
    protected $primaryKey = 'bo_id';
    public $timestamps = true;

    const STATUS_FIRST_TOUCH = 1;
    const STATUS_REQUIRE_CONFIRM = 2;
    const STATUS_COMPETITOR_ANALYSIS = 3;
    const STATUS_BUSINESS_NEGOTIATION = 4;
    const STATUS_WON_CLOSE = 5;
    const STATUS_LOST_CLOSE = 6;

    const STATUS_MAP = [
        '1' => '初次接触',
        '2' => '需求确认',
        '3' => '竞品分析',
        '4' => '商务谈判',
        '5' => '赢单关闭',
        '6' => '丢单关闭',
    ];
    const TODO_SUGGEST = [
        1 => [ // 初步接触
            ['id' => 1, 'title' => '明确客户的真实需求和当前问题', 'description' => '了解客户面临的具体问题、痛点，以及需求是否明确具体。若客户表达模糊或仅为随意交流，需警惕后续成交可能性低', 'is_finished' => 0],
            ['id' => 2, 'title' => '找到并建立与关键联系人的沟通', 'description' => '识别能影响决策的关键人并建立有效沟通。若联系人频繁更换或沟通不畅，说明项目推进难度大，需调整策略', 'is_finished' => 0],
        ],
        2 => [ // 需求确认
            ['id' => 3, 'title' => '明确客户的具体需求和应用场景', 'description' => '让客户把需求和使用场景说清楚，需求越详细越好。若客户说不清楚，要主动引导，避免后期反复修改影响进度', 'is_finished' => 0],
            ['id' => 4, 'title' => '搞清楚客户的预算和审批流程', 'description' => '了解客户大概预算和谁能拍板、流程怎么走。若客户不愿透露预算或流程，说明项目优先级不高，要注意投入产出比', 'is_finished' => 0],
        ],
        3 => [ // 竞品分析
            ['id' => 5, 'title' => '了解客户在对比哪些方案', 'description' => '知道客户还在看哪些竞品、关注点是什么。如果客户只是随便看看，采购意愿不强，要判断是否值得继续投入精力', 'is_finished' => 0],
            ['id' => 6, 'title' => '明确我们相对竞品的优势', 'description' => '搞清楚客户对竞品和我们的看法，突出我们的优势。如果客户只关心价格或明显偏向其他家，要警惕被“陪标”或只是被用来压价', 'is_finished' => 0],
        ],
        4 => [ // 商务谈判
            ['id' => 7, 'title' => '明确客户的采购时间表和流程', 'description' => '知道客户什么时候能定下来、每一步怎么走。如果客户迟迟不给时间表或流程，项目可能会拖延或被搁置，要及时调整跟进方式', 'is_finished' => 0],
            ['id' => 8, 'title' => '搞清楚合同和付款的关键条款', '了解合同签署、付款方式和主要商务条款。如果客户反复修改合同或拖延签约，说明内部还没统一意见，要多和关键决策人沟通，避免时间浪费', 'is_finished' => 0],
        ],
        5 => [ // 赢单关闭
            ['id' => 9, 'title' => '完成合同签署和项目交接', 'description' => '确保合同已签署、相关资料和信息已交接给实施或服务团队，及时跟进客户后续需求，维护客户关系，为二次销售或转介绍做准备', 'is_finished' => 0],
            ['id' => 10, 'title' => '总结本次销售过程的经验和不足', 'description' => '复盘整个销售流程，记录成功经验和遇到的问题，优化后续销售策略，提升个人能力', 'is_finished' => 0],
        ],
        6 => [ // 丢单关闭
            ['id' => 11, 'title' => '明确丢单原因并记录', 'description' => '了解客户未选择我们的真实原因（如价格、产品、时机等），详细记录，便于后续分析和改进', 'is_finished' => 0],
            ['id' => 12, 'title' => '维护好客户关系，争取后续机会', 'description' => '即使丢单也要保持良好沟通，表达感谢，争取未来有合作机会或客户转介绍，避免因丢单影响个人和公司口碑', 'is_finished' => 0],
        ],
    ];

    public static function getAllBeforeTodoSuggest($status)
    {
        $list = [];
        foreach (self::TODO_SUGGEST as $i => $items) {
            if ($i >= $status) {
                break;
            }
            $list = array_merge($list, $items);
        }
        return $list;
    }

    protected function suggestTodoFinishIds(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => $value ? explode(',', $value) : [],
            set: fn(string|array $value) => is_array($value) ? implode(',', array_unique($value)) : $value
        );
    }

    public static function getTodoSuggest($status, array $finishIds = [])
    {
        $list = self::TODO_SUGGEST[$status] ?? [];
        foreach ($list as &$item) {
            $item['is_finished'] = (int)in_array($item['id'], $finishIds);
        }
        return $list;
    }


    public static function addOne($data)
    {
        return (new static)->saveData($data);
    }

    public static function list($uid, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->get()->toArray();
    }

    public static function getOne($uid, $id, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->where("bo_id", $id)
            ->first();
    }

    public static function updOne($uid, $id, $data)
    {
        return self::where("user_id", $uid)
            ->where('bo_id', $id)
            ->update($data);
    }

    public static function delById($id, $userId = null)
    {
        $res = 0;
        !is_array($id) && $id = [$id];
        // Db::transaction(function () use ($id, $userId, &$res) { // 不使用事务,避免客户/商机/待办关联删除产生死锁
            $res = parent::delById($id, $userId);
            Attachment::delByClassifyId(Attachment::CLASSIFY_BUSINESS, $id, $userId);
            $todoIds = TodoItems::getUserQuery($userId)->whereIn('bo_id', $id)->pluck('id')->toArray();
            $todoIds && TodoItems::delById($todoIds, $userId);
        // });
        return $res;
    }

    public static function edit($uid, $id, $data)
    {
        return self::where("user_id", $uid)
            ->find($id)
            ?->saveData($data);
    }

    public function saveData(array &$data)
    {
        $id = $data['id'] ?? 0;
        if (!empty($data['customer_id']) && $data['customer_id'] != $this['customer_id']) {
            $info = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
            if (empty($info)) {
                throw new BusinessException('客户信息不存在');
            }
        }
        Db::transaction(function () use ($data, $id) {
            $userId = $data['user_id'];
            if (!empty($data['sort_no'])) { // 拖拽排序
                self::moveSortNo($userId, $this['sort_no'], $data['sort_no']);
            }
            if (empty($id)) { // 新增
                $data['sort_no'] = self::getMinSortNo($userId) - 1;
            } else {
                if (!empty($data['title']) && $this['title'] != $data['title']) {
                    $attachment = Attachment::getClassifyRootDir(Attachment::CLASSIFY_BUSINESS, $id, $userId, ['id']);
                    if ($attachment) {
                        $attachment->name = $data['title'];
                        $attachment->save();
                    }
                }
            }
            $this->fill($data)->save();
            if (empty($id)) {
                $attachment = new Attachment();
                $parent_id = 0;
                if (!empty($data['customer_id'])) {
                    $parentId = $attachment::where('classify', Attachment::CLASSIFY_CUSTOMER)->where('classify_id', $data['customer_id'])->value('id');
                    $parent_id = $parentId ?: 0;
                }
                $attachment->fill([
                    'user_id' => $data['user_id'],
                    'parent_id' => $parent_id,
                    'classify' => Attachment::CLASSIFY_BUSINESS,
                    'classify_id' => $this[$this->primaryKey],
                    'name' => $this['title'],
                    'type' => 'dir',
                ])->save();
            }
        });

        return $this[$this->primaryKey];
    }

}