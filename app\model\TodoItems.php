<?php

namespace app\model;

use app\pyapi\service\LinkService;
use app\wxwork\service\NoticeService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use support\Db;
use support\Log;
use support\Model;
use Webman\Exception\BusinessException;

class TodoItems extends BaseModel
{
    use SoftDeletes;

    protected $table = 'todo_items';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $fillable = [
        'user_id',
        'customer_id',
        'bo_id',
        'title',
        'description',
        'due_date',
        'priority',
        'is_finished',
        'sort_no',
        'notify_time',
        'notify_cycle',
        'type',
    ];

    const PAGE_DETAIL_ROUTE = 'pages/template/todo-details/index?id='; // 待办详情路由

    const PRIORITY_LOW = 10; //低
    const PRIORITY_MID = 20; //中
    const PRIORITY_HIGH = 30; //高

    const TYPE_NORMAL = 1; // 普通待办
    const TYPE_AI = 2; // AI待办
    const TYPE_LABELS = [
        self::TYPE_NORMAL => '普通待办',
        self::TYPE_AI => 'AI待办',
    ];

    const NOTIFY_CYCLE_NONE = 0; //无
    const NOTIFY_CYCLE_DAY = 1; //每天提醒
    const NOTIFY_CYCLE_WORKDAY = 2; // 工作日提醒
    const NOTIFY_CYCLE_WEEKEND = 3; // 周末提醒
    const NOTIFY_CYCLE_WEEK = 4; // 每周提醒
    const NOTIFY_CYCLE_TWO_WEEK = 5; // 每两周提醒
    const NOTIFY_CYCLE_MONTH = 6; // 每月提醒
    const NOTIFY_CYCLE_THREE_MONTH = 7; // 每三个月提醒
    const NOTIFY_CYCLE_HALF_YEAR = 8; // 每半年提醒
    const NOTIFY_CYCLE_YEAR = 9; // 每年提醒
    const NOTIFY_CYCLE_UNITS = [
        self::NOTIFY_CYCLE_DAY => [1, 'day'],
        self::NOTIFY_CYCLE_WORKDAY => [1, 'day'],
        self::NOTIFY_CYCLE_WEEKEND => [1, 'day'],
        self::NOTIFY_CYCLE_WEEK => [7, 'day'],
        self::NOTIFY_CYCLE_TWO_WEEK => [14, 'day'],
        self::NOTIFY_CYCLE_MONTH => [1, 'month'],
        self::NOTIFY_CYCLE_THREE_MONTH => [3, 'month'],
        self::NOTIFY_CYCLE_HALF_YEAR => [6, 'month'],
        self::NOTIFY_CYCLE_YEAR => [1, 'year'],
    ];
    const NOTIFY_CYCLE_LABELS = [
        self::NOTIFY_CYCLE_NONE => '无',
        self::NOTIFY_CYCLE_DAY => '每天',
        self::NOTIFY_CYCLE_WORKDAY => '工作日',
        self::NOTIFY_CYCLE_WEEKEND => '周末',
        self::NOTIFY_CYCLE_WEEK => '每周',
        self::NOTIFY_CYCLE_TWO_WEEK => '每两周',
        self::NOTIFY_CYCLE_MONTH => '每月',
        self::NOTIFY_CYCLE_THREE_MONTH => '每三个月',
        self::NOTIFY_CYCLE_HALF_YEAR => '每半年',
        self::NOTIFY_CYCLE_YEAR => '每年',
    ];


    public static function getOne($userId, $id, $fields = ['*'])
    {
        return self::select($fields)
            ->where('user_id', $userId)
            ->where('id', $id)
            ->first();
    }

    public static function delOne($userId, $id)
    {
        return self::delById($id, $userId);
    }

    public static function addOne($data)
    {
        return (new static)->saveData($data);
    }

    public static function updateOne($userId, $id, $data)
    {
        return self::where('user_id', $userId)
            ->find($id)
            ?->saveData($data);
    }

    public static function editOne($userId, $id, $data)
    {
        return self::where('user_id', $userId)
            ->where('id', $id)
            ->update($data);
    }

    public static function getQuery($uid, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid);
    }

    /**
     * 获取客户下当前时间前的最后一条待办时间
     */
    public static function getLastDueDateByCustomer($uid, array $customerIds)
    {
        return array_column(self::getUserQuery($uid, Db::raw('customer_id, MAX(due_date) as due_date'))
            ->whereIn('customer_id', $customerIds)
            ->where('due_date', '<=', date('Y-m-d H:i:s'))
            ->groupBy('customer_id')
            ->get()
            ->toArray(), 'due_date', 'customer_id');
    }

    /**
     * 获取商机下当前时间后的第一条待办
     */
    public static function getAfterFirstByBusiness($uid = null, array $boIds = [], string $fields = '*')
    {
        return static::getFirstOfEveryGroup('bo_id', 'due_date ASC', $uid, function (Builder $query) use ($boIds) {
            $boIds ? $query->whereIn('bo_id', $boIds) : $query->where('bo_id', '>', 0);
            $query->where('due_date', '>', date('Y-m-d H:i:s'));
        }, $fields);
    }

    /**
     * 获取商机下当前时间前的最后一条待办时间在指定时间范围内的待办
     */
    public static function getBeforeLastByBusinessAndTodoDueDate(array $dueDate, $uid = null, array $boIds = [], string $fields = '*')
    {
        $query = $uid ? static::getUserQuery($uid, $fields) : static::select($fields);
        $boIds ? $query->whereIn('bo_id', $boIds) : $query->where('bo_id', '>', 0);
        $query->where('due_date', '<', date('Y-m-d H:i:s'))
            ->groupBy('bo_id')
            ->havingRaw('MAX(due_date) BETWEEN ? AND ?', $dueDate);
        var_dump($query->toRawSql());
        return $query->get()->toArray();
    }

    public static function getActiveList($uid, $fields = ['*'], $startDate = null, $endDate = null)
    {
        $query = self::getQuery($uid, $fields);
        if ($startDate && $endDate) {
            $startDate = date('Y-m-d 00:00:00', strtotime($startDate));
            $endDate = date('Y-m-d 23:59:59', strtotime($endDate));

            // 逾期任务 或者 在指定时间段内到期的任务
            $query = $query->where(function ($q) use ($startDate, $endDate) {
                $q->where('due_date', '<', date('Y-m-d 00:00:00')) // 逾期任务
                ->orWhereBetween('due_date', [$startDate, $endDate]); // 或者时间段内到期的任务
            });
        }

        Log::info($query->toSql());
        return $query->where('is_finished', 0)
            ->get()
            ->toArray();
    }

    public static function completedList($uid, $fields = ['*'])
    {
        return self::getQuery($uid, $fields)
            ->where('is_finished', 1)
            ->orderBy('id', 'desc')
            ->limit(20)
            ->get()
            ->toArray();
    }

    public static function getByDate($uid, $dates, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->where("due_date", $dates)
            ->first();
    }

    public function saveData(array &$data)
    {
        $oldData = $this->toArray();
        $data['user_id'] = $userId = getUserId();
        $id = $data['id'] ?? 0;
        $isAdd = empty($id);

        //判断是否有时间冲突: 暂时不判断时间冲突
        if (!empty($data['due_date'])) {
//            $info = TodoItems::getByDate($data['user_id'], $data['due_date'], ['id', 'title', 'description']);
//            if (!empty($info) && $info['id'] != $id) {
//                throw new BusinessException('该时间点已经有待办事项了');
//            }
        }
        //兼容前端传空字符串情况
        if (isset($data['bo_id'])) {
            $data['bo_id'] = $data['bo_id'] ?: 0;
        }
        if (isset($data['customer_id'])) {
            $data['customer_id'] = $data['customer_id'] ?: 0;
        }
        if (isset($data['priority'])) {
            $data['priority'] = $data['priority'] ?: 0;
        }
        // 日期时间兼容空字符串
        if (isset($data['due_date'])) {
            $data['due_date'] = $data['due_date'] ?: null;
        }
        if (isset($data['notify_time'])) {
            $data['notify_time'] = $data['notify_time'] ?: null;
        }
        if (!empty($data['bo_id']) && $data['bo_id'] != $this['bo_id']) {
            $info = BusinessOpportunities::getOne($data['user_id'], $data['bo_id'], ['bo_id']);
            if (empty($info)) {
                throw new BusinessException('商业信息不存在');
            }
        }
        if (!empty($data['customer_id']) && $data['customer_id'] != $this['customer_id']) {
            $info = Customers::getOne($data['user_id'], $data['customer_id'], ['customer_id']);
            if (empty($info)) {
                throw new BusinessException('客户信息不存在');
            }
        }
        if (!empty($data['notify_cycle'])) {
            if (empty($data['id']) || $data['notify_cycle'] != $this['notify_cycle']) {
                $date = date('Y-m-d');
                $notifyTime = !empty($data['notify_time']) ? date("Y-m-d H:i:00", strtotime($data['notify_time'])) : "$date 09:00:00";
                if (strtotime($notifyTime) <= time()) {
                    $notifyTime = TodoItems::getNextCycleNotifyTime($notifyTime, $data['notify_cycle']);
                } else {
                    // 上一个周期时间还没过的话从上一个周期开始
                    $prevNotifyTime = TodoItems::getNextCycleNotifyTime($notifyTime, $data['notify_cycle'], true);
                    if (strtotime($prevNotifyTime) > time()) {
                        $notifyTime = $prevNotifyTime;
                    }
                }
                $data['notify_time'] = $notifyTime;
            }
        }
        Db::transaction(function () use ($data, $userId, $oldData) {
            if (!empty($data['sort_no'])) { // 拖拽排序
                self::moveSortNo($userId, $oldData['sort_no'], $data['sort_no']);
            }
            $this->fill($data)->save();
        });
        if (!$isAdd) {
            // 编辑了排序字段后重新生成排序号进行排序
            foreach (['is_finished', 'customer_id', 'priority', 'due_date'] as $key) {
                if (isset($data[$key]) && $data[$key] != $oldData[$key]) {
                    $data['sort_no'] = self::findNewSortNo($userId, $this['id']);
                    self::moveSortNo($userId, $oldData['sort_no'], $data['sort_no']);
                    self::updateSortNo($userId, $this['id'], $data['sort_no']);
                    break;
                }
            }
        }
        $saveId = $this[$this->primaryKey];
        if ($isAdd) { // 新增完后排序找到位置插入排序号并把后面的排序号+1
            $rn = self::findNewSortNo($userId, $saveId);
            $rn && Db::transaction(function () use ($rn, $userId, $saveId) {
                self::getUserQuery($userId)->where('sort_no', '>=', $rn)->increment('sort_no');
                self::updateSortNo($userId, $saveId, $rn);
            });
        }
        return $saveId;
    }

    public static function updateSortNo($userId, $id, $sortNo)
    {
        self::getUserQuery($userId)->where('id', $id)->update(['sort_no' => $sortNo]);
    }

    /**
     * 寻找元素新的排序号
     */
    public static function findNewSortNo($userId, $id)
    {
        $subquery = self::getUserQuery($userId, Db::raw('id, ROW_NUMBER() OVER (ORDER BY is_finished ASC, IF(customer_id > 0, 1, 0) DESC, IF(priority >= 20, priority, 0) DESC, due_date ASC, id ASC) AS rn'));
        $info = Db::table($subquery, 't')->where('id', $id)->first();
        return $info ? $info->rn : 0;
    }

    public static function delById($id, $userId = null)
    {
        !is_array($id) && $id = [$id];
        $sortList = self::getUserQuery($userId)
            ->whereIn('id', $id)
            ->orderBy('sort_no')
            ->pluck('sort_no')
            ->toArray();
        if (empty($sortList)) {
            throw new BusinessException('待办事项不存在');
        }
        $res = 0;
        Db::transaction(function () use ($id, $userId, $sortList, &$res) {
            $res = parent::delById($id, $userId);
            // 删除后把后面的排序号-1
            $amount = 1;
            $sortNoRaw = 'sort_no - (CASE ';
            foreach ($sortList as $i => $sortNo) {
                $opt = isset($sortList[$i + 1]) ? "BETWEEN $sortNo AND " . $sortList[$i + 1] : '>' . $sortNo;
                $sortNoRaw .= "WHEN sort_no $opt THEN $amount ";
                $amount++;
            }
            $sortNoRaw .= 'ELSE 0 END)';
            self::getUserQuery($userId)->where('sort_no', '>', $sortList[0])->update(['sort_no' => Db::raw($sortNoRaw)]);
        });
        return $res;
    }


    //格式化待办事项的due_date 日期
    public static function genDueDate($dueDate)
    {
        if (empty($dueDate)) {
            return '';
        }
        $hi = date('H:i', strtotime($dueDate));
        switch ($hi) {
            case '00:00':
            case '53:59':
                return date('Y-m-d', strtotime($dueDate));
                break;
        }
        return date('Y-m-d H:i', strtotime($dueDate));
    }

    public static function getPriorityLabel($priority)
    {
        switch ($priority) {
            case static::PRIORITY_LOW:
                return '低';
            case static::PRIORITY_MID:
                return '中';
            case static::PRIORITY_HIGH:
                return '高';
            default:
                return '中';
        }
    }

    public static function getNotifyList()
    {
        $datetime = date('Y-m-d H:i');
        return TodoItems::whereBetween('notify_time', [$datetime . ':00', $datetime . ':59'])
            ->where(function (Builder $query) use ($datetime) {
                $query->where('due_date', '>=', $datetime . ':00')
                    ->orWhereNull('due_date');
            })
            ->where('is_finished', 0)
            ->get(['id', 'user_id', 'customer_id', 'bo_id', 'title', 'description', 'notify_time', 'notify_cycle', 'type'])
            ->toArray();
    }

    public static function getCycleNotifyList()
    {
        $time = strtotime('2025-05-08');//time();
        $datetime = date('Y-m-d H:i:s', $time);
        $nowWeek = date('w', $time); // 当前时间是星期几, 0-6
        $nowYear = date('Y', $time);
        $nowWeekNum = date('W', $time) + $nowYear * 52; // 当前时间是第几周
        $nowMonthNum = date('n', $time) + $nowYear * 12; // 当前时间是第几月
        $nowDay = date('d', $time);
        $list = TodoItems::where('due_date', '>', $datetime)
            ->where('is_finished', 0)
            ->where('notify_cycle', '>', 0)
            ->get(['id', 'user_id', 'title', 'created_at', 'notify_cycle'])
            ->toArray();
        // 判断当前时间是否在符合周期提醒时间
        $result = [];
        foreach ($list as $v) {
            $createTime = strtotime($v['created_at']);
            $createdYear = date('Y', $createTime);
            $createdWeekNum = date('W', $createTime) + $createdYear * 52; // 创建时间是第几周
            $createdMonthNum = date('n', $createTime) + $createdYear * 12; // 创建时间是第几月
            $diffWeek = $nowWeekNum - $createdWeekNum;
            $diffMonth = $nowMonthNum - $createdMonthNum;
            $cycle = $v['notify_cycle'];
            if ($cycle == self::NOTIFY_CYCLE_DAY ||
                ($cycle == self::NOTIFY_CYCLE_WORKDAY && $nowWeek != 6 && $nowWeek != 0) ||
                ($cycle == self::NOTIFY_CYCLE_WEEKEND && ($nowWeek == 6 || $nowWeek == 0)) ||
                ($cycle == self::NOTIFY_CYCLE_WEEK && $nowWeek == 1) ||
                ($cycle == self::NOTIFY_CYCLE_TWO_WEEK && $nowWeek == 1 && $diffWeek >= 2 && $diffWeek % 2 == 0) ||
                ($cycle == self::NOTIFY_CYCLE_MONTH && $nowDay == '01') ||
                ($cycle == self::NOTIFY_CYCLE_THREE_MONTH && $nowDay == '01' && $diffMonth >= 3 && $diffMonth % 3 == 0) ||
                ($cycle == self::NOTIFY_CYCLE_HALF_YEAR && $nowDay == '01' && $diffMonth >= 6 && $diffMonth % 6 == 0) ||
                ($cycle == self::NOTIFY_CYCLE_YEAR && $nowDay == '01' && $nowMonthNum % 12 == 1)
            ) {
                $result[] = $v;
            }
        }
        return $result;
    }

    /**
     * 获取下次周期提醒时间
     *
     * @param string $notifyTime 最近一次通知时间(首次创建取创建日期+通知时间的时分秒,没有则默认9点)
     * @param int $cycle 通知周期
     */
    public static function getNextCycleNotifyTime(string $notifyTime, $cycle, $prev = false)
    {
        $unitItem = self::NOTIFY_CYCLE_UNITS[$cycle] ?? [];
        if (empty($unitItem)) {
            return null;
        }
        $week = date('w');
        if ($cycle == self::NOTIFY_CYCLE_WORKDAY) { // 工作日周期通知时下一天通知跳过周末
            $nextMap = [5 => 3, 6 => 2]; // 下一天间隔大于1天的星期
            $unitItem[0] = $nextMap[$week] ?? 1;
        } elseif ($cycle == self::NOTIFY_CYCLE_WEEKEND && $week == 0) { // 周末周期通知时下一天通知跳过工作日
            $unitItem[0] = $week == 6 ? 1 : 6 - $week;
        }
        return date('Y-m-d H:i:s', strtotime($notifyTime . ($prev ? ' -' : ' +') . $unitItem[0] . ' ' . $unitItem[1]));
    }

    public static function pushTodoNotice($userId, $id, string $title)
    {
        $pageUrl = LinkService::genTodoDetail($id);
        // $shareUrl = ShortLink::genShortUrl($userId, $pageUrl);
        $msg = "⚠ 待处理：\n";
        NoticeService::pushNotice($userId, $msg . "\n[$title]($pageUrl)", payload: ['url' => $pageUrl], pushContent: $msg . $title);
    }

    public static function pushAiTodoNotice($userId, $id, string $result, $taskId)
    {
        $info = self::getOne($userId, $id, ['title']);
        if (empty($info)) {
            Log::info("AI待办任务(id=$id)不存在");
            return;
        }
        NoticeService::pushNotice($userId, $result, payload: ['url' => LinkService::genChat()], taskId: $taskId);
    }

}