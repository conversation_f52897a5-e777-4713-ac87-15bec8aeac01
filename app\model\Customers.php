<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;
use library\tool;
use support\Db;
use support\Model;
use Webman\Exception\BusinessException;

class Customers extends BaseModel
{
    use SoftDeletes;

    protected $table = 'customers';
    protected $primaryKey = 'customer_id';
    public $timestamps = true;
    protected $fillable = [
        'user_id',
        'company_name',
        'company_short_name',
        'contact_name',
        'customer_level',
        'contact_remark',
        'contact_title',
        'phone',
        'notes',
        'sort_no',
        'contact_birthday_y',
        'contact_birthday_m',
        'contact_birthday_d',
        'contact_profile',
    ];

    const LEVEL_PRE = 10;
    const LEVEL_NORMAL = 20;
    const LEVEL_IMPORTANT = 30;
    const LEVEL_LABELS = [
        self::LEVEL_PRE => '潜在客户',
        self::LEVEL_NORMAL => '普通客户',
        self::LEVEL_IMPORTANT => '重要客户',
    ];

    const LEVEL_VALUES = [
        '潜在客户' => self::LEVEL_PRE,
        '普通客户' => self::LEVEL_NORMAL,
        '重要客户' => self::LEVEL_IMPORTANT,
    ];

    public static function getOne($uid, $id, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->where("customer_id", $id)
            ->first();
    }

    public static function getByName($uid, $name, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->where("company_name", $name)
            ->first();
    }

    public static function getAllByBoIds(array $boIds, $uid = null, $fields = ['*'])
    {
        $query = $uid ? static::getUserQuery($uid, $fields) : static::select($fields);
        $subquery = $uid ? BusinessOpportunities::getUserQuery($uid, 'customer_id') : BusinessOpportunities::select('customer_id');
        return $query->whereIn("customer_id", $subquery->whereIn('bo_id', $boIds))
            ->get()
            ->toArray();
    }

    public static function getAllByBirthday($m, $d, $y = null, $uid = null, $fields = ['*'])
    {
        $query = $uid ? static::getUserQuery($uid, $fields) : static::select($fields);
        $y && $query->where('contact_birthday_y', $y);
        return $query->where('contact_birthday_m', $m)
            ->where('contact_birthday_d', $d)
            ->get()
            ->toArray();
    }

    public static function list($uid, $fields = ['*'])
    {
        return self::select($fields)
            ->where("user_id", $uid)
            ->get()->toArray();
    }

    public static function getListByIds($ids, $uid, $fields = ['*'])
    {
        return self::select($fields)
            ->whereIn('customer_id', $ids)
            ->where("user_id", $uid)
            ->get()->toArray();
    }

    public static function del($uid, $id)
    {
        return self::delById($id, $uid);
    }

    public static function delById($id, $userId = null)
    {
        !is_array($id) && $id = [$id];
        $boIds = BusinessOpportunities::getUserQuery($userId)
            ->whereIn("customer_id", $id)
            ->pluck("bo_id")
            ->toArray();
        $res = 0;
        // Db::transaction(function () use ($id, $userId, $boIds, &$res) { // 不使用事务,避免客户/商机/待办关联删除产生死锁
            $res = parent::delById($id, $userId);
            Attachment::delByClassifyId(Attachment::CLASSIFY_CUSTOMER, $id, $userId);
            $boIds && BusinessOpportunities::delById($boIds, $userId);
            $todoIds = TodoItems::getUserQuery($userId)->whereIn('customer_id', $id)->pluck('id')->toArray();
            $todoIds && TodoItems::delById($todoIds, $userId);
        // });
        return $res;
    }

    public static function edit($uid, $id, $data)
    {
        return self::where("user_id", $uid)
            ->find($id)
            ?->saveData($data);
    }

    public static function addOne($data)
    {
        return (new static)->saveData($data);
    }

    public function saveData(array &$data)
    {
        // 查看是否已存在同名客户
        $userId = getUserId();
        $id = $data['id'] ?? 0;
        if (!empty($data['company_name'])) {
            $info = self::getByName($userId, $data['company_name'], ['customer_id']);
            if (!empty($info) && $info['customer_id'] != $id) {
                throw new BusinessException('客户已存在');
            }
        }
        if (!empty($data['contact_birthday'])) {
            $ymd = tool::getYmdFromDate($data['contact_birthday']);
            $data['contact_birthday_y'] = $ymd[0];
            $data['contact_birthday_m'] = $ymd[1];
            $data['contact_birthday_d'] = $ymd[2];
        }
        Db::transaction(function () use ($data, $userId, $id) {
            if (!empty($data['sort_no'])) { // 拖拽排序
                self::moveSortNo($userId, $this['sort_no'], $data['sort_no']);
            }
            $oldData = $this->toArray();
            if (empty($id)) { // 新增
                $data['sort_no'] = self::getMinSortNo($userId) - 1;
            }
            $this->fill($data)->save();
            if (empty($id)) {
                (new Attachment())->fill([
                    'user_id' => $userId,
                    'classify' => Attachment::CLASSIFY_CUSTOMER,
                    'classify_id' => $this[$this->primaryKey],
                    'name' => $this['company_short_name'] ?: ($this['company_name'] ?: $this['contact_name']),
                    'type' => 'dir',
                ])->save();
            } else {
                if (!empty($data['company_short_name']) && $oldData['company_short_name'] != $data['company_short_name'] || !empty($data['company_name']) && $oldData['company_name'] != $data['company_name'] || !empty($data['contact_name']) && $oldData['contact_name'] != $data['contact_name']) {
                    $attachment = Attachment::getClassifyRootDir(Attachment::CLASSIFY_CUSTOMER, $id, $userId, ['id']);
                    if ($attachment) {
                        $attachment->name = $this['company_short_name'] ?: ($this['company_name'] ?: $this['contact_name']);
                        $attachment->save();
                    }
                }
            }
        });
        return $this[$this->primaryKey];
    }

}