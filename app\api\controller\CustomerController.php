<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\BaseModel;
use app\model\Customers;
use app\model\CustomersContact;
use app\model\TodoItems;
use library\tool;
use Respect\Validation\Validator as v;
use support\Db;
use support\Request;
use Webman\Exception\BusinessException;

class CustomerController extends BaseController
{
    protected $indexFields = ['customer_id', 'company_name', 'company_short_name', 'contact_name', 'sort_no', 'contact_birthday_y', 'contact_birthday_m', 'contact_birthday_d'];
    protected $indexFilterFields = [
        'company_name' => 'like',
        'company_short_name' => 'like',
        'customer_id' => 'in',
    ];
    protected $indexSort = [
        'sort_no' => 'asc',
    ];
    protected $contactModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new Customers();
        $this->contactModel = new CustomersContact();
    }

    protected function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        $model->saveData($data);
    }

    public function detail(Request $request)
    {
        $request->setGet(array_merge($request->get(), ['id' => $request->input('customer_id')]));
        return parent::detail($request);
    }

    protected function afterDetail(Request $request, &$data)
    {
        $uid = getUserId();
        $data['contact_birthday'] = tool::formatDate($data['contact_birthday_y'], $data['contact_birthday_m'], $data['contact_birthday_d']);
        $data['contact_list'] = $this->contactModel->getUserQuery($uid, array_merge(['id'], $this->contactModel->getFillable()))->where('customer_id', $data['customer_id'])->get()->toArray();
        $contactList = [];
        foreach ($data['contact_list'] as $item) {
            $item['contact_birthday'] = tool::formatDate($item['contact_birthday_y'], $item['contact_birthday_m'], $item['contact_birthday_d']);
            $contactList[] = $item;
        }
        $data['contact_list'] = $contactList;
    }

    protected function afterList(Request $request, array &$data)
    {
        $uid = getUserId();
        if (!empty($data['list'])) {
            $customerIds = array_column($data['list'], 'customer_id');
            $todoMap = TodoItems::getLastDueDateByCustomer($uid, $customerIds, ['id', 'customer_id', 'due_date', 'title']);
            foreach ($data['list'] as &$item) {
                $item->last_todo_time = $todoMap[$item->customer_id] ?? null;
                $item['contact_birthday'] = tool::formatDate($item['contact_birthday_y'], $item['contact_birthday_m'], $item['contact_birthday_d']);
            }
        }
    }

    public function del(Request $request)
    {
        $request->setPost(array_merge($request->post(), ['id' => $request->input('customer_id')]));
        return parent::del($request);
    }

}