<?php

namespace app\api\service;

use app\model\Attachment;
use app\model\User;
use app\model\UserChat;
use app\pyapi\service\LinkService;
use app\wxwork\service\NoticeService;
use GuzzleHttp\Client;
use library\ai\qwen;
use library\Alioss;
use library\IdEncode;
use library\tool;
use support\Db;
use support\Log;
use Webman\Exception\BusinessException;

class AttachmentService
{
    protected $ossUtil;
    const READ_TYPE = ["txt", "md", "xlsx", "xls", "csv", "json", "xml", "html", "docx", "pdf", "pptx", "ppt", "doc", "wav", "pcm", "ogg-opus", "speex", "silk", "mp3", "m4a", "aac", "amr", "jpeg", "jpg", "png", "gif", "bmp", "webp", "tiff", "tif", "ico"];

    public function __construct()
    {
        $this->ossUtil = new Alioss();
    }

    public function saveParseFile(string $url, string $fileType, $userId, $attachmentId, string $fileName)
    {
        if (!in_array($fileType, self::READ_TYPE)) {
            return [
                'status' => 'fail',
                'message' => '文件类型不支持'
            ];
        }
        $client = new Client(['timeout' => 10]);
        $response = $client->post('http://' . env('AI_AGENT_SERVER') . '/save_parse_file', [
            'json' => [
                'url' => $url,
                'user_id' => $userId,
                'attachment_id' => $attachmentId,
                'file_type' => $fileType,
                'file_name' => $fileName,
            ]
        ]);
        $result = $response->getBody()->getContents();
        $result && $result = json_decode($result, true);
        if (empty($result) || $result['status'] != 'success') {
            throw new BusinessException($result['message']?? 'AI服务异常'); 
        }
        return $result;
    }

    public function delParseFile($userId, $attachmentIds)
    {
        $client = new Client(['timeout' => 10]);
        $response = $client->post('http://' . env('AI_AGENT_SERVER') . '/del_parse_file', [
            'json' => [
                'user_id' => $userId,
                'attachment_ids' => $attachmentIds,
            ]
        ]);
        $result = $response->getBody()->getContents();
        $result && $result = json_decode($result, true);
        if (empty($result) || $result['status'] != 'success') {
            throw new BusinessException($result['message']?? 'AI服务异常'); 
        }
        return $result;
    }

    public function vectorAdd(string $url, string $fileType, $userId, $attachmentId, string $fileName, bool $saveParseFile = false)
    {
        if (!in_array($fileType, self::READ_TYPE)) {
            return [
                'status' => 'fail',
                'message' => '文件类型不支持'
            ];
        }
        $client = new Client(['timeout' => 10]);
        $response = $client->post('http://' . env('AI_AGENT_SERVER') . '/vector_add', [
            'json' => [
                'url' => substr($url, 0, 4) == 'http' ? $url : $this->ossUtil->genGetSign($url)['sign_url'],
                'user_id' => $userId,
                'attachment_id' => $attachmentId,
                'file_type' => $fileType,
                'file_name' => $fileName,
                'save_parse_file' => $saveParseFile,
            ]
        ]);
        $result = $response->getBody()->getContents();
        $result && $result = json_decode($result, true);
        if (empty($result) || $result['status'] != 'success') {
            throw new BusinessException($result['message']?? 'AI服务异常'); 
        }
        return $result;
    }

    public function vectorDel(array $ids, $userId, array $attachmentIds)
    {
        $client = new Client(['timeout' => 10]);
        $response = $client->post('http://' . env('AI_AGENT_SERVER') . '/vector_del', [
            'json' => [
                'ids' => $ids,
                'user_id' => $userId,
                'attachment_ids' => $attachmentIds,
            ]
        ]);
        $result = $response->getBody()->getContents();
        $result && $result = json_decode($result, true);
        if (empty($result) || $result['status'] != 'success') {
            throw new BusinessException($result['message']?? 'AI服务异常'); 
        }
        return $result;
    }

    public function withMoveFile(array $oldData, array $newData)
    {
        if (empty($newData['id'])) { // 新增不处理
            return;
        }
        if (isset($newData['classify'])) {
            // 移出销售物料
            if ($oldData['classify'] == Attachment::CLASSIFY_PRODUCT && $newData['classify'] != Attachment::CLASSIFY_PRODUCT) {
                $this->delParseFile($oldData['user_id'], [$oldData['id']]);
            }
            // 移入销售物料
            if ($newData['classify'] == Attachment::CLASSIFY_PRODUCT && $oldData['classify'] != Attachment::CLASSIFY_PRODUCT) {
                $this->saveParseFile($oldData['url'], $oldData['type'], $oldData['user_id'], $oldData['id'], $oldData['name']);
            }
        }
    }

    public function autoClassifyByAttachmentIds($userId, array $attachmentIds = [])
    {
        $query = Attachment::getUserQuery($userId, ['id', 'url', 'name', 'type'])
            ->where('classify', '=', 0)
            ->whereIn('type', self::READ_TYPE)
            ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-1 day')));
        $attachmentIds && Attachment::whereIn('id', $attachmentIds);
        $attachments = $query->get()->toArray();
        $archiveList = [];
        foreach ($attachments as $attachment) {
            try {
                $data = $this->autoClassify($attachment['url'], $attachment['name'], $attachment['type'], $userId, $attachment['id']);
                Attachment::updateById($attachment['id'], $data);
                if (!empty($data['archive_info'])) {
                    $data['archive_info']['url'] = AttachmentService::genViewUrlById($attachment['id'], $attachment['type']);
                    $archiveList[] = $data['archive_info'];
                }
            } catch (\Throwable $th) {
                Log::error('自动归档失败', ['error' => $th->getMessage(), 'trace' => $th->getTraceAsString(), 'attachment' => $attachment]);
                continue;
            }
        }
        $archiveList && AttachmentService::pushArchiveNotice($userId, $archiveList);
        return $archiveList;
    }

    public function autoClassify(string $url, string $fileName, string $fileType, $userId, $attachmentId)
    {
        // 读取文件内容
        $content = '';
        $readUrl = '';
        if (in_array($fileType, self::READ_TYPE)) {
            $client = new Client(['timeout' => 30]);
            $readUrl = substr($url, 0, 4) == 'http' ? $url : $this->ossUtil->genGetSign($url)['sign_url'];
            $response = $client->post('http://' . env('AI_AGENT_SERVER') . '/doc_parse', [
                'json' => [
                    'url' => $readUrl,
                    'file_type' => $fileType,
                    'read_size' => 200,
                ]
            ]);
            $result = $response->getBody()->getContents();
            $result && $result = json_decode($result, true);
            if (empty($result) || $result['status'] != 'success') {
                throw new BusinessException($result['message']?? 'AI服务异常'); 
            }
            $content = $result['data']['content'];
        }
        // 获取目录树
        $fields = ['id', 'parent_id', 'name', 'type', 'classify', 'classify_id'];
        $query = Attachment::getUserQuery($userId, $fields);
        Attachment::makeListQuery($userId, ['classify' => Attachment::CLASSIFY_PROJECT], $query, $fields);
        $list = Db::table($query, 't')->where('type', 'dir')->get()->toArray();
        $classifyNameMap = [1 => '客户文件', 2 => '商机文件', 3 => '销售物料'];
        foreach ($list as &$item) {
            $item = (array)$item;
            $item['classify_name'] = $classifyNameMap[$item['classify']] ?? '';
        }
        $tree = tool::buildTree($list);
        $userInfo = json_encode(User::getById($userId, ['name', 'mobile', 'company', 'profile', 'business_type']), JSON_UNESCAPED_UNICODE);
        $tree[] = ['id' => 0, 'parent_id' => 0, 'name' => '销售物料根目录(其余classify=3的为其子目录)', 'type' => 'dir', 'classify' => Attachment::CLASSIFY_PRODUCT, 'classify_id' => 0];
        // 请求AI判断目录
        $prompt = "# 目标和要求\n现在我有一个文件需要你根据消息上下文(如果上下文消息中指定了这个文件要存储的目录那么就直接匹配这个目录)、文件名和文件内容从提供的json目录树中找到最合适的一个目录，并返回该目录的id、classify和classify_id字段组成的json对象，不要有其他任何说明文字，如果没有匹配到目录就返回空对象\n\n# 相关信息\n## 用户信息\n$userInfo\n\n## 文件目录classify说明\nclassify为1表示客户文件，classify为2表示商机文件，classify为3表示销售物料(指可对外复用的公司和产品相关标准资料，比如公司介绍、产品介绍、方案模板、报价模板、案例、技术文档等，适用于所有客户，不针对单一客户或商机)\n\n## 文件信息\n文件名: $fileName\nattachment_id: $attachmentId\n\n## 文件内容\n$content\n\n## 目录树\n" . json_encode($tree, JSON_UNESCAPED_UNICODE);
        $messages = UserChat::getMessages($userId, 5);
        $messages[] = ['role' => 'user', 'content' => $prompt];
        $result = qwen::send($messages, qwen::MODEL_MAX, 0.5, 'json');
        $message = $result['choices'][0]['message']['content'] ?? '';
        $data = json_decode($message, true);
        $returnData = [
            'parent_id' => 0,
            'classify' => Attachment::CLASSIFY_OTHER,
            'classify_id' => 0,
        ];
        if ($data) {
            isset($data['id']) && $returnData['parent_id'] = $data['id'];
            isset($data['classify']) && $returnData['classify'] = $data['classify'];
            isset($data['classify_id']) && $returnData['classify_id'] = $data['classify_id'];
        }
        if ($returnData['classify'] == Attachment::CLASSIFY_PRODUCT && $readUrl) {
            $this->saveParseFile($readUrl, $fileType, $userId, $attachmentId, $fileName);
        }
        // 根据id从目录树遍历出完整的路径
        if ($returnData['parent_id']) {
            $path = '客户资料';
            $nodes = tool::findPathFromTree($returnData['parent_id'], $tree);
            foreach ($nodes as $node) {
                $path .= '/'. $node['name'];
            }
        } else {
            $path = $returnData['classify'] == Attachment::CLASSIFY_PRODUCT ? '销售物料' : '/';
        }
        $this->vectorAdd($url, $fileType, $userId, $attachmentId, $fileName, $returnData['classify'] == Attachment::CLASSIFY_PRODUCT);
        $returnData['archive_info'] = [
            'file_name' => $fileName,
            'path' => $path,
        ];
        // var_dump($prompt, $result, $returnData);
        return $returnData;
    }

    public static function pushArchiveNotice($userId, array $archiveList)
    {
        $pushContent = $content = "📂 文件已成功归档​​";
        foreach ($archiveList as $i => $item) {
            $itemContent = "\n- {$item['file_name']} \n📍 路径： {$item['path']}";
            $content .= $itemContent . "\n{$item['url']}";
            $pushContent .= $itemContent;
        }
        // NoticeService::pushNotice($userId, $content, addToChat: true, payload: ['url' => LinkService::genChat()], pushContent: $pushContent);
    }

    // 通过附件id生成文件预览url
    public static function genViewUrlById($id, string $fileType = '')
    {
        return env('API_DOMAIN', '') . '/a/' . IdEncode::encode($id) . ($fileType? '.'. $fileType: '');
    }

    // 通过附件id重定向到文件url
    public static function redirectViewUrlById($id)
    {
        $id = IdEncode::decode(explode('.', $id)[0]); // 去除文件后缀
        if (empty($id)) {
            return Response('403 Forbidden', 403);
        }
        $info = Attachment::find($id, ['id', 'type', 'url']);
        if (empty($info) || empty($info['url'])) {
            return Response('404 Not Found', 404);
        }
        return redirect($info['url']);
    }

    public static function clearTempFile()
    {
        return Attachment::where('classify', Attachment::CLASSIFY_TEMP)
            ->where('created_at', '<', date('Y-m-d H:i:s', strtotime('-3 day')))
            ->delete();
    }

    public function clearDeletedFile()
    {
        $clearDate = date('Y-m-d', strtotime('-30 day'));
        $objects = Attachment::onlyTrashed()
            ->where('url', '<>', '')
            ->whereBetween('deleted_at', [$clearDate . ' 00:00:00', $clearDate . ' 23:59:59'])
            ->pluck(Db::raw('url as uri'))
            ->toArray();
        $objects && $this->ossUtil->deleteObjects($objects);
        return $objects;
    }

}