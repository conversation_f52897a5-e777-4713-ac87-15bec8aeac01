<?php

namespace app\api\service;

use app\model\Customers;
use app\model\CustomersContact;
use app\pyapi\service\LinkService;
use app\wxwork\service\NoticeService;
use support\Log;

class CustomerService
{

    public static function contactBirthdayRemind()
    {
        $tomorrow = explode('-', date('Y-m-d', strtotime('+1 day')));
        $list = Customers::getAllByBirthday((int)$tomorrow[1], (int)$tomorrow[2], fields: ['user_id', 'customer_id', 'contact_name', 'company_short_name', 'contact_title', 'phone']);
        foreach ($list as $item) {
           self::pushContactBirthdayRemind($item['user_id'], $item['contact_name'], $item['company_short_name'], $item['contact_title'], "{$tomorrow[1]}月{$tomorrow[2]}日（明天）", $item['phone']);
        }
        $contacts = CustomersContact::getAllByBirthday((int)$tomorrow[1], (int)$tomorrow[2], fields: ['user_id', 'customer_id', 'contact_name','contact_title', 'phone']);
        $customerIds = array_column($contacts, 'customer_id');
        $companyMap = array_column(Customers::select(['customer_id', 'company_short_name'])->whereIn('customer_id', $customerIds)->get()->toArray(), null, 'customer_id');
        foreach ($contacts as $item) {
            self::pushContactBirthdayRemind($item['user_id'], $item['contact_name'], $companyMap[$item['customer_id']]['company_short_name']?? '', $item['contact_title'], "{$tomorrow[1]}月{$tomorrow[2]}日（明天）", $item['phone']);
        }
    }

    public static function pushContactBirthdayRemind($userId, string $name, string $company, string $job, string $birthday, string $phone)
    {
        NoticeService::pushNotice($userId, "​​📅 生日提醒​​\n\n{$name}（{$company} {$job}）\n{$birthday}\n电话：{$phone}", addToChat: true, payload: ['url' => LinkService::genChat()]);
    }
}