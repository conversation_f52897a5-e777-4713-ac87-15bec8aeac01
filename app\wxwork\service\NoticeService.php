<?php

namespace app\wxwork\service;

use app\model\UserChat;
use Webman\RedisQueue\Redis;

class NoticeService
{

    //给用户推送通知
    public static function pushNotice($userId, $content, $delay = 0, $addToChat = true, $payload = null, $taskId = 0, $pushContent = '')
    {
        $time = time();
        $hour = date('G', $time);
        if ($hour > 7 && $hour < 22) { // 晚上10点到早上7点不发通知
            Redis::send('notice', [
                'user_id' => $userId,
                'content' => $pushContent ?: $content,
                'payload' => $payload,
            ], $delay);
        }
        if ($addToChat) {
            UserChat::addOne([
                'user_id' => $userId,
                'content' => json_encode([['role' => 'assistant', 'content' => $content]], JSON_UNESCAPED_UNICODE),
                'is_robot' => 1,
                'session_id' => WxUserService::getSessionId($userId),
                'task_id' => $taskId,
            ]);
        }
    }

}