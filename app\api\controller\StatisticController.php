<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\BusinessOpportunities;
use app\model\Customers;
use Respect\Validation\Validator as v;
use support\Db;
use support\Request;
use Webman\Exception\BusinessException;

class StatisticController extends BaseController
{
    public function index(Request $request)
    {
        $uid = getUserId();
        $data['customer_count'] = Customers::getUserQuery($uid)->count();
        $data['business_sum'] = number_format(BusinessOpportunities::getUserQuery($uid)
            ->whereNot('status', BusinessOpportunities::STATUS_LOST_CLOSE)
            ->sum('budget'), 0);
        $data['business_won_sum'] = number_format(BusinessOpportunities::getUserQuery($uid)
            ->where('status', BusinessOpportunities::STATUS_WON_CLOSE)
            ->sum('budget'), 0);
        $boStageCount = array_column(BusinessOpportunities::getUserQuery($uid, Db::raw('status, count(*) as count'))
            ->groupBy('status')
            ->get()
            ->toArray(), 'count', 'status');
        $boList = BusinessOpportunities::getUserQuery($uid, ['title', 'status'])
            ->whereIn('status', array_keys(BusinessOpportunities::STATUS_MAP))
            ->get()
            ->toArray();
        $businessStageList = [];
        foreach ($boList as $item) {
            $stage = $item['status'];
            if (empty($businessStageList[$stage])) {
                $businessStageList[$stage] = []; 
            }
            unset($item['status']);
            $businessStageList[$stage][] = $item;
        }
        $data['business_stage_count'] = [];
        $data['business_stage_list'] = [];
        $businessTotal = 0;
        $lostCloseCount = $boStageCount[BusinessOpportunities::STATUS_LOST_CLOSE]?? 0;
        foreach (BusinessOpportunities::STATUS_MAP as $stage => $name) {
            if ($stage == BusinessOpportunities::STATUS_LOST_CLOSE) {
                continue;
            }
            $businessTotal += $boStageCount[$stage]?? 0;
            $data['business_stage_count'][$name] = $boStageCount[$stage]?? 0;
            $data['business_stage_list'][$name] = $businessStageList[$stage]?? [];
        }
        // $i = $beforeTotal = 0;
        // foreach (BusinessOpportunities::STATUS_MAP as $stage => $name) { // 第一阶段包含全部, 后面的每一阶段包含下面不含丢单关闭的所有阶段
        //     $stage != BusinessOpportunities::STATUS_LOST_CLOSE && $data['business_stage_count'][$name] = $i == 0 ? $businessTotal : $businessTotal - $beforeTotal - $lostCloseCount;
        //     $beforeTotal += $boStageCount[$stage]?? 0;
        //     $i++;
        // }
        $businessWinCount = $boStageCount[BusinessOpportunities::STATUS_WON_CLOSE]?? 0;
        $data['business_total_trans_rate'] = $businessTotal > 0 && $businessWinCount > 0 ? round($businessWinCount / $businessTotal * 100, 2): 0;
        $data['business_lost_close_rate'] = $businessTotal > 0 && $lostCloseCount > 0? round($lostCloseCount / $businessTotal * 100, 2): 0;
        return $this->success($data);
    }
}