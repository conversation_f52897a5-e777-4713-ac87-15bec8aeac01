<?php

namespace app\command;

use support\Db;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand('migrate:user', 'migrate user')]
class MigrateUser extends Command
{
    /**
     * @return void
     */
    protected function configure()
    {
        $this->addArgument('uid', InputArgument::REQUIRED, '用户ID');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $uid = $input->getArgument('uid');
        Db::connection()
        $output->writeln('Hello migrate:user');
        return self::SUCCESS;
    }

}
