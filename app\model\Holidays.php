<?php

namespace app\model;

use app\pyapi\service\LinkService;
use app\wxwork\service\NoticeService;
use Illuminate\Database\Eloquent\SoftDeletes;
use library\ai\qwen;
use support\Log;
use support\Model;

class Holidays extends Model
{
    use SoftDeletes;

    protected $table = 'holidays';
    protected $primaryKey = 'id';
    public $timestamps = true;

    const GREETING_MAP = [
        '元旦' => '🎉新年祯祥！
时序更迭处，万象启新元。
祝您财源广进通四海，鸿运当头福满门！
过往厚爱铭感于心，新年征程静候佳音。',
        '春节' => '🧨春祺夏安！
骏马驰新岁，青云步坦途。
愿您事业腾骧开胜景，阖家欢乐庆丰年！
旧岁相携情深义重，新春企盼再续辉煌。',
        '元宵节' => '🏮元宵嘉夜！
灯月交辉，星桥火树。
愿您事业似滚元宵圆满，生活如花灯璀璨！
上元良宵遥寄心语，春暖花开静待相逢。',
        '清明节' => '🌱清明和煦！
惠风拂新绿，明德继世长。
愿您心拥春光生万象，业承厚德纳千祥！
前路虽远步履不停，诚盼时节共话新机。',
        '劳动节' => '🌾敬颂勤安！
耕耘结硕果，奋斗书华章。
祝您暂卸鞍马养精锐，整装再攀更高峰！
承蒙往日关照提携，静待节后共展宏图。',
        '端午节' => '🌿端午安康！
粽香传情谊，竞渡启新程。
愿您身体康泰常伴，事业乘风破浪！
承蒙一路鼎力支持，静待佳节后再叙合作。',
        '中秋节' => '🌕中秋喜乐！
月映山河美，人和万事兴。
祝您阖家团圆享天伦，商海扬帆立潮头！
感恩选择相伴至今，诚邀金秋共绘新章。',
        '国庆节' => '🎇国庆同欢！
盛世华章谱，丹心赤子情。
愿您宏图更展凌云志，家业双丰日月新！
得遇信任何其有幸，来日方长携手并肩。',
        '圣诞节' => '🎄冬绥顺遂！
松柏凝翠，岁聿其莫。
愿您新年启程千帆竞，旧岁功成百事祥！
虽隔重洋心念常系，静待春归共话新章。',
    ];

    public static function getByYear($year, $fields = ['*'])
    {
        return self::select($fields)
            ->where('year', $year)
            ->first();
    }

    public static function getByDates($dates, $fields = ['*'])
    {
        return self::select($fields)
            ->where('dates', $dates)
            ->first();
    }

    public static function addOne($data)
    {
        return self::insertGetId($data);
    }

    public static function syncHolidays($year)
    {
        $info = static::getByYear($year, ['id']);
        if (!empty($info)) {
            return true;
        }
        $res = \library\Holidays::getHolidays($year);
        if (empty($res)) {
            //没有数据
            Log::info('还没有节假日数据');
            return true;
        }
        $res = json_decode($res, true);
        foreach ($res as $k => $v) {
            static::addOne([
                'year' => $year,
                'dates' => $v['date'],
                'name' => $v['name'],
                'isOffDay' => $v['isOffDay'],
            ]);
        }
        return true;
    }

    //判断是否是节假日: isOffDay(1,假日； 0，补班日)
    public static function isHoliday($date)
    {
        $info = self::getByDates($date, ['name', 'isOffDay']);
        if (empty($info)) {
            return false;
        }
        return $info;
    }

    public static function isHolidayFirstDay(string $date)
    {
        $info = self::getByDates($date, ['name', 'isOffDay']);
        if (empty($info) || $info['isOffDay'] != 1) {
            return false;
        }
        $prevInfo = self::getByDates(date('Y-m-d', strtotime('-1 day', strtotime($date))), ['name', 'isOffDay']);
        if (!empty($prevInfo) && $prevInfo['isOffDay'] == 1 && $prevInfo['name'] == $info['name']) {
            return false;
        }
        return $info['name'];
    }

    /**
     * 推送节日祝福给所有用户
     *
     * @param string $name 假日名称
     */
    public static function pushHolidayGreetingToAllUser(string $name)
    {
        $customerIds = BusinessOpportunities::where('customer_id', '>', 0)->pluck('customer_id')->toArray();
        $customerList = $customerIds ? Customers::whereIn('customer_id', $customerIds)->get(['customer_id', 'user_id', 'company_short_name', 'contact_name', 'contact_title', 'phone'])->toArray() : [];
        $userCustomers = [];
        foreach ($customerList as $customer) {
            if (empty($customer['contact_name'])) {
                continue;
            }
            !isset($userCustomers[$customer['user_id']]) && $userCustomers[$customer['user_id']] = '';
            $userCustomers[$customer['user_id']] .= "$customer[contact_name]（$customer[company_short_name] $customer[phone]）\n";
        }
        $greeting = self::GREETING_MAP[$name] ?? '';
        $userList = User::query()->get(['id', 'name', 'company'])->toArray();
        $suffix = $name == '清明节' ? '安康' : '快乐';
        foreach ($userList as $user) {
            $content = "{$user['name']}，{$name}{$suffix}！🎉 节日是联络客户的好时机！";
            if ($greeting) {
                $content .= "\n\n以下祝福可复制发送给您的客户：\n$greeting\n{$user['company']} {$user['name']}恭祝！";
            }
            $customerContent = $userCustomers[$user['id']] ?? '';
            if ($customerContent) {
                $content .= "\n\n推荐联系：\n$customerContent";
            }
            NoticeService::pushNotice($user['id'], $content, payload: ['url' => LinkService::genChat()]);
            Log::info("{$name}祝福语已推送: $content");
        }
    }

}