<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use support\Log;
use support\Request;
use app\process\Http;

global $argv;

return [
    'webman' => [
        'handler' => Http::class,
        'listen' => 'http://0.0.0.0:' . env('APP_SERVER_PORT'),
        'count' => cpu_count() * 4,
        'user' => '',
        'group' => '',
        'reusePort' => false,
        'eventLoop' => '',
        'context' => [
            'ssl' => array(
                'enable' => true,
                'local_cert' => '/www/ssl/apitest.profly.com.cn.pem', // 也可以是crt文件
                'local_pk' => '/www/ssl/apitest.profly.com.cn.key',
                'verify_peer' => false,
                'allow_self_signed' => true, //如果是自签名证书需要开启此选项
            )
        ],
        'constructor' => [
            'requestClass' => Request::class,
            'logger' => Log::channel('default'),
            'appPath' => app_path(),
            'publicPath' => public_path()
        ]
    ],
    // File update detection and automatic reload
    'monitor' => [
        'handler' => app\process\Monitor::class,
        'reloadable' => false,
        'constructor' => [
            // Monitor these directories
            'monitorDir' => array_merge([
                app_path(),
                config_path(),
                base_path() . '/process',
                base_path() . '/support',
                base_path() . '/resource',
                base_path() . '/.env',
            ], glob(base_path() . '/plugin/*/app'), glob(base_path() . '/plugin/*/config'), glob(base_path() . '/plugin/*/api')),
            // Files with these suffixes will be monitored
            'monitorExtensions' => [
                'php', 'html', 'htm', 'env'
            ],
            'options' => [
                'enable_file_monitor' => !in_array('-d', $argv) && DIRECTORY_SEPARATOR === '/',
                'enable_memory_monitor' => DIRECTORY_SEPARATOR === '/',
            ]
        ]
    ],
//    'wechat_websocket' => [
//        'handler' => app\WechatPusher::class,
//        'listen' => 'websocket://0.0.0.0:' . env('WECHAT_WEBSOCKET_PORT'),
//        'count' => 1,
//    ],
    'ScheduleTask' => [
        'handler' => app\process\ScheduleTask::class
    ],
    'ScheduleTask1' => [
        'handler' => app\process\ScheduleTask1::class
    ],
];
