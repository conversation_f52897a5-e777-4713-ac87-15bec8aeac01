<?php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DATABASE_HOST', '127.0.0.1'),
            'port' => env('DATABASE_PORT', 3306),
            'database' => env('DATABASE_NAME', 'saleagent'),
            'username' => env('DATABASE_USERNAME', 'root'),
            'password' => env('DATABASE_PASSWORD', '123456'),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'options' => [
                PDO::ATTR_EMULATE_PREPARES => false, // Must be false for Swoole and Swow drivers.
            ],
            'pool' => [
                'max_connections' => intval(env('MYSQL_POOL_MAX_CONNECTIONS')),
                'min_connections' => intval(env('MYSQL_POOL_MIN_CONNECTIONS')),
                'wait_timeout' => 3,
                'idle_timeout' => 60,
                'heartbeat_interval' => 50,
            ],
        ],
    ],
];