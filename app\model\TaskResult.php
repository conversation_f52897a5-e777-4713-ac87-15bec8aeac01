<?php

namespace app\model;


use Illuminate\Database\Eloquent\SoftDeletes;

class TaskResult extends BaseModel
{
    use SoftDeletes;

    protected $table = 'task_result';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $fillable = ['user_id', 'task_id', 'result'];

    public static function getOneByTaskId($userId, $taskId, $fields = ['*']) {
        return static::getUserQuery($userId, $fields)->where('task_id', $taskId)->first();
    }


    public static function getByTaskId($userId, $taskId)
    {
        return self::where('user_id', $userId)
            ->where('task_id', $taskId)->first();
    }

}