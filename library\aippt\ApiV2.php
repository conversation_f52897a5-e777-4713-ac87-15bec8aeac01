<?php

namespace library\aippt;

use library\aippt\HttpUtils;
use support\Log;

/**
 * Docmee AI PPT V2 API
 * 参考V1设计，保持简洁易用
 */
class ApiV2
{
    const BASE_URL = "https://open.docmee.cn";

    /**
     * 复用V1的getToken方法
     */
    public static function getToken($uid)
    {
        return Api::getToken($uid);
    }

    /**
     * V2 创建任务
     * 
     * @param string $apiToken 认证token
     * @param int $type 类型：1.智能生成 2.上传文件生成 3.上传思维导图生成 4.word转ppt 5.网页链接生成 6.文本内容生成 7.Markdown大纲生成
     * @param string|null $content 内容
     * @param array $files 文件列表 [['path' => '', 'name' => ''], ...]
     * @return string 任务ID
     */
    public static function createTask($apiToken, $type, $content = null, $files = [])
    {
        Log::info('Creating Docmee V2 task', [
            'type' => $type,
            'content_length' => $content ? mb_strlen($content) : 0,
            'files_count' => count($files)
        ]);

        $url = self::BASE_URL . "/api/ppt/v2/createTask";
        $headers = [
            "token" => $apiToken
        ];

        // 构建multipart数据
        $multipart = [
            ['name' => 'type', 'contents' => (string)$type]
        ];

        if ($content !== null) {
            $multipart[] = ['name' => 'content', 'contents' => $content];
        }

        // 添加文件
        foreach ($files as $file) {
            if (isset($file['path'], $file['name'])) {
                $multipart[] = [
                    'name' => 'file[]',
                    'contents' => fopen($file['path'], 'r'),
                    'filename' => $file['name']
                ];
            }
        }

        $resp = HttpUtils::postMultipart($url, $headers, $multipart);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("创建V2任务失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("创建V2任务异常：" . $json["message"]);
        }

        Log::info('Docmee V2 task created', ['task_id' => $json["data"]["id"]]);
        return $json["data"]["id"];
    }

    /**
     * V2 生成大纲内容
     * 
     * @param string $apiToken 认证token
     * @param string $taskId 任务ID
     * @param array $options 生成选项
     * @return string 生成的内容
     */
    public static function generateContent($apiToken, $taskId, $options = [])
    {
        Log::info('Generating Docmee V2 content', ['task_id' => $taskId]);

        $url = self::BASE_URL . "/api/ppt/v2/generateContent";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode(array_merge([
            "id" => $taskId,
        ], $options));

        $sb = [];
        $resp = HttpUtils::postSse($url, $headers, $body, function ($data) use (&$sb) {
            $json = json_decode($data, true);
            if (array_key_exists("status", $json) && $json["status"] == -1) {
                throw new \RuntimeException("生成V2内容异常：" . $json["error"]);
            }
            if (array_key_exists("text", $json)) {
                $text = $json['text'];
                array_push($sb, $text);
            }
        });

        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("生成V2内容失败，httpStatus=" . $resp["statusCode"]);
        }
        if (strpos($resp["contentType"], 'application/json') !== false) {
            $json = json_decode($resp["text"], true);
            throw new \RuntimeException("生成V2内容异常：" . $json["message"]);
        }

        Log::info('Docmee V2 content generated', ['task_id' => $taskId]);
        return implode('', $sb);
    }

    /**
     * V2 生成PPT
     * 
     * @param string $apiToken 认证token
     * @param string $taskId 任务ID
     * @param array $options 生成选项
     * @return array PPT信息
     */
    public static function generatePptx($apiToken, $taskId, $options = [])
    {
        Log::info('Generating Docmee V2 PPTX', ['task_id' => $taskId]);

        $url = self::BASE_URL . "/api/ppt/v2/generatePptx";
        $headers = [
            "token" => $apiToken
        ];
        $body = json_encode(array_merge([
            "id" => $taskId,
        ], $options));

        $resp = HttpUtils::postJson($url, $headers, $body);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("生成V2 PPT失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("生成V2 PPT异常：" . $json["message"]);
        }

        Log::info('Docmee V2 PPTX generated', [
            'task_id' => $taskId,
            'ppt_info' => $json["data"]['pptInfo']
        ]);
        return $json["data"]['pptInfo'];
    }

    /**
     * V2 获取生成选项
     * 
     * @param string $apiToken 认证token
     * @param string $lang 语言代码
     * @return array 选项数据
     */
    public static function getOptions($apiToken, $lang = 'zh')
    {
        $url = self::BASE_URL . "/api/ppt/v2/options?lang=" . $lang;
        $headers = [
            "token" => $apiToken
        ];

        $resp = HttpUtils::get($url, $headers);
        if ($resp["statusCode"] != 200) {
            throw new \RuntimeException("获取V2选项失败，httpStatus=" . $resp["statusCode"]);
        }
        $json = json_decode($resp["text"], true);
        if ($json["code"] != 0) {
            throw new \RuntimeException("获取V2选项异常：" . $json["message"]);
        }
        return $json["data"];
    }

    /**
     * V2 一键生成PPT（简化版本）
     *
     * @param string $apiToken 认证token
     * @param int $type 生成类型
     * @param string $content 内容
     * @param array $options 生成选项
     * @param array $files 文件列表
     * @return array PPT信息
     */
    public static function generatePptComplete($apiToken, $type, $content, $options = [], $files = [])
    {
        Log::info('Starting complete V2 PPT generation', [
            'type' => $type,
            'content_length' => mb_strlen($content)
        ]);

        // 1. 创建任务
        $taskId = self::createTask($apiToken, $type, $content, $files);

        // 2. 生成内容
        $generatedContent = self::generateContent($apiToken, $taskId, $options);

        // 3. 生成PPT，传递生成的内容
        $pptOptions = array_merge($options, ['markdown' => $generatedContent]);
        $pptData = self::generatePptx($apiToken, $taskId, $pptOptions);

        Log::info('Complete V2 PPT generation finished', [
            'task_id' => $taskId,
        ]);

        return array_merge($pptData, ['taskId' => $taskId]);
    }

    /**
     * 验证内容长度
     */
    public static function validateContentLength($type, $content)
    {
        $limits = [
            1 => 1000,    // 智能生成：不超过1000字符
            6 => 20000,   // 文本内容：不超过20000字符
        ];

        if (isset($limits[$type]) && mb_strlen($content) > $limits[$type]) {
            throw new \RuntimeException("内容长度不能超过{$limits[$type]}字符");
        }
    }

    /**
     * 获取生成类型说明
     */
    public static function getTypeDescription($type)
    {
        $types = [
            1 => '智能生成（主题、要求）',
            2 => '上传文件生成',
            3 => '上传思维导图生成',
            4 => '通过word精准转ppt',
            5 => '通过网页链接生成',
            6 => '粘贴文本内容生成',
            7 => 'Markdown大纲生成',
        ];
        return $types[$type] ?? '未知类型';
    }
}
