<?php

namespace app\pyapi\service;

use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\ShortLink;
use app\model\TodoItems;
use library\IdEncode;
use support\exception\BusinessException;
use support\Log;

class LinkService
{
    const LINK_TODO_LIST = 1;
    const LINK_TODO_DETAIL = 2;
    const LINK_CUSTOMER_LIST = 3;
    const LINK_CUSTOMER_DETAIL = 4;
    const LINK_BUSI_LIST = 5;
    const LINK_BUSI_DETAIL = 6;
    const LINK_BUSI_STATIC = 7;
    const LINK_FILE_LIST = 8;
    const LINK_PPT_EDIT = 9;

    public static function genLink($uid, $type, $thirdId)
    {
        $title = '';
        $link = '';
        switch ($type) {
            case static::LINK_TODO_LIST:
                $title = '我的待办列表';
                $link = LinkService::genShortLink($uid, LinkService::genTodoList());
                break;
            case static::LINK_TODO_DETAIL:
                if (empty($thirdId)) {
                    throw new BusinessException('缺少待办ID参数');
                }
                $info = TodoItems::getOne($uid, $thirdId, ['title']);
                $title = $info['title'] ?? '待办详情';
                $link = LinkService::genShortLink($uid, LinkService::genTodoDetail($thirdId));
                break;
            case static::LINK_CUSTOMER_LIST:
                $title = '我的客户列表';
                $link = LinkService::genShortLink($uid, LinkService::genCustomerList());
                break;
            case static::LINK_CUSTOMER_DETAIL:
                if (empty($thirdId)) {
                    throw new BusinessException('缺少客户ID(customer_id)参数');
                }
                $title = '客户详情';
                $link = LinkService::genShortLink($uid, LinkService::genCustomerDetail($thirdId));
                break;
            case static::LINK_BUSI_LIST:
                $title = '我的商机列表';
                $link = LinkService::genShortLink($uid, LinkService::genBusiList());
                break;
            case static::LINK_BUSI_DETAIL:
                if (empty($thirdId)) {
                    throw new BusinessException('缺少商机ID(bo_id)参数');
                }
                $info = BusinessOpportunities::getOne($uid, $thirdId, ['title']);
                $title = $info['title'] ?? '商机详情';
                $link = LinkService::genShortLink($uid, LinkService::genBusiDetail($thirdId));
                break;
            case static::LINK_BUSI_STATIC:
                $title = '销售漏斗';
                $link = LinkService::genShortLink($uid, LinkService::genBusiStatic());
                break;
            case static::LINK_FILE_LIST:
                $title = '存储文件地址';
                $link = LinkService::genShortLink($uid, LinkService::genFilePath());
                break;
            case static::LINK_PPT_EDIT:
                $title = 'ppt';
                $link = LinkService::genShortLink($uid, LinkService::genPptEdit($thirdId));
                break;
        }
        return [
            'title' => $title,
            'link' => $link,
        ];
    }

    public static function genShortLink($uid, $url)
    {

        return env('WEB_DOMAIN') . $url;
        try {
            return ShortLink::genShortUrl($uid, $url);
        } catch (\Exception $e) {
            Log::error($e->getTraceAsString());
            return '';
        }
    }

    //待办列表
    public static function genTodoList()
    {
        return '/pages/tabBar/todo/todo';
    }

    //待办detail
    public static function genTodoDetail($id)
    {
        return '/pages/template/todo-details/index?id=' . $id;
    }

    //客户列表
    public static function genCustomerList()
    {
        return '/pages/tabBar/customer/customer';
    }

    //客户detail
    public static function genCustomerDetail($id)
    {
        return '/pages/template/customer-details/index?customer_id=' . $id;
    }

    //商机列表
    public static function genBusiList()
    {
        return '/pages/tabBar/business/business';
    }

    //客户detail
    public static function genBusiDetail($id)
    {
        return '/pages/template/business-detail/index?business_id=' . $id;
    }

    public static function genBusiStatic()
    {
        return '/pages/tabBar/statistics/statistics';
    }

    //思考h5
    public static function genTaskResult($taskId)
    {
        return '/pages/ai-task?id=' . $taskId;
    }

    //我的文件目录
    public static function genFilePath()
    {
        return '/pages/userInfo/related-documents/index';
    }

    // 聊天页
    public static function genChat()
    {
        return '/pages/ai-chat';
    }

    //PPT编辑页
    public static function genPptEdit($pptId)
    {
        return '/pages/ppt-editor/index?pptId=' . $pptId;
    }
}