<?php

namespace app\api\controller;

use app\controller\BaseController;
use support\Request;

class AsrController extends BaseController
{
    protected $client;
    public function __construct()
    {
        parent::__construct();
        $this->client = new \library\asr\AliAsr;
    }

    public function token(Request $request)
    {
        return $this->success($this->client->getToken());
    }
}