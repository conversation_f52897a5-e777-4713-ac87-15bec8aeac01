<?php

namespace app\queue\redis\slow;

use app\model\UserChat;
use app\wxwork\service\AiTaskService;
use app\wxwork\service\CoreService;
use library\redisKeys;
use support\Log;
use support\Redis;

/**
 * 企微应用- 处理ai任务的结果
 */

use Webman\RedisQueue\Consumer;

/**
 * 处理ai任务的结果
 */
class AiWxwork implements Consumer
{
    // 要消费的队列名
    public $queue = 'ai-task-wxwork';

    public $connection = 'default';

    /**
     * 消费
     * data: app_id, user_id, user_wxid, content, query
     **/
    public function consume($data)
    {
        //
        //Log::info('开始消费队列---' . $this->queue);
        //Log::info(var_export($data, true));

        if (empty($data['task_id']) || empty($data['user_id'])) {
            Log::error('ai-task队列，参数错误 data=' . var_export($data, true));
            return;
        }


        $taskId = $data['task_id'];
        $userId = $data['user_id'];

        //get结果
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        $count = 1;

        $result = []; //存储最后结果
        $isDone = '';
        $resContent = '';
        Log::info(
            '开始遍历结果'
        );
        while (true) {
            if ($count >= 600) {
                break;
            }
            //是否完成
            $res = Redis::lIndex($key, -1);
            if ($res) {
                $res = json_decode($res, true);
                if (!empty($res['event']) && $res['event'] == 'RunCompleted') {
                    Log::info('redis 查询到RunCompleted  已经执行完毕');
                    $isDone = 1;
                    $resContent = trim($res['content']);
                    break;
                }
            }
            sleep(1);
            $count++;
        }
        Log::info('遍历结束');
        if ($isDone == 1) {
            //结束了，需要将结果让ai重新总结一下
            //AiTaskService::summary($data['app_id'], $userId, $data['user_wxid'], $taskId, $data['query'], $resContent);
            AiTaskService::finished($data['app_id'], $userId, $data['user_wxid'], $taskId, $resContent);
            return true;
        }
        return $this->end('执行超时', $data);
    }


    public function end($taskId, $data)
    {
        Log::info('queue  success----');
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        Redis::rPush($key, json_encode(['event' => 'RunCompleted', 'content' => '执行超时']));
        CoreService::sendMsg($data['app_id'], $data['user_wxid'], '执行超时', CoreService::MSG_TYPE_TEXT);
        AiTaskService::saveResult($data['user_id'], $taskId);
        return false;
    }

}