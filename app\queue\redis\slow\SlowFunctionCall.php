<?php

namespace app\queue\redis\slow;

use support\Log;
use Webman\RedisQueue\Consumer;
use Webman\RedisQueue\Redis as RedisQueue;

/**
 * 慢函数异步调用
 */
class SlowFunctionCall implements Consumer
{
    // 要消费的队列名
    const QUEUE_NAME = 'slow-function-call';
    public $queue = self::QUEUE_NAME;

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';

    /**
     * 消费
     * data:  class, method, params
     **/
    public function consume($data)
    {
        Log::info("开始消费{$this->queue}队列: ". var_export($data, true));
        if (empty($data['class']) || empty($data['method'])) {
            Log::error(self::QUEUE_NAME . '队列，参数错误 data=' . var_export($data, true));
            return;
        }
        $class = $data['class'];
        $method = $data['method'];
        $params = $data['params']?? [];
        $obj = new $class;
        $obj->$method(...$params);
    }

    public static function send(string $class, string $method, array $params = [], $delay = 0)
    {
        RedisQueue::send(self::QUEUE_NAME, ['class' => $class, 'method' => $method, 'params' => $params], $delay);
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        // 无需反序列化
        Log::error(self::QUEUE_NAME . '队列消费失败，data=【' . var_export($package, true) . '】 error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
    }
}