<?php

namespace app\queue\redis\fast;

use app\model\User;
use app\model\UserWxwork;
use app\wxwork\service\CoreService;
use GuzzleHttp\Client;
use support\Log;
use Webman\RedisQueue\Consumer;

class Notice implements Consumer
{

    // 要消费的队列名
    public $queue = 'notice';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';

    const APP_ID = 1;

    /**
     * 消费
     * data:  user_id, content
     **/
    public function consume($data)
    {
        Log::info('notice 队列 - 消费 data=' . var_export($data, true));
        // $user = UserWxwork::getByUserIdAppId(static::APP_ID, $data['user_id']);
        // !empty($user['third_id']) && CoreService::sendMsg(static::APP_ID, $user['third_id'], $data['content'], CoreService::MSG_TYPE_TEXT);
        $user = User::getById($data['user_id'], ['cid']);
        if (!empty($user['cid'])) {
            // 如果没有标题则取内容第一行作为标题
            if (empty($data['title'])) {
                $rows = explode("\n", $data['content']);
                $data['title'] = mb_substr($rows[0], 0, 50);
                $data['content'] = mb_substr(ltrim(implode("\n", array_slice($rows, 1))), 0, 256);
            }
            $params = [
                'push_clientid' => $user['cid'],
                'title' => $data['title'],
                'content' => $data['content'],
                "category" => ["harmony" => "WORK", "huawei" => "WORK", "vivo" => "WORK"],
                "options" => [
                    "HW" => [
                        "/message/android/category" => "WORK"
                    ]
                ]
            ];
            !empty($data['payload']) && $params['payload'] = $data['payload'];
            $client = new Client(['timeout' => 10, 'verify' => false]);
            $response = $client->post('https://fc-mp-a7dd4948-ad0b-43b1-8471-b9c97dd12c84.next.bspapp.com/shpush', [
                'json' => $params
            ]);
            Log::info('notice 队列 - 推送app消息结果: ' . $response->getBody()->getContents());
        }
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        // 无需反序列化
        Log::error('notice队列消费失败，data=【' . var_export($package, true) . '】 error=【' . $e->getMessage() . '】');
    }

}
