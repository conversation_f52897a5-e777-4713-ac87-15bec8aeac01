<?php

namespace library;

use AlibabaCloud\SDK\Imm\V20200930\Imm;
use AlibabaCloud\SDK\Imm\V20200930\Models\RefreshWebofficeTokenRequest;
use AlibabaCloud\SDK\Imm\V20200930\Models\WebofficePermission;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Imm\V20200930\Models\GenerateWebofficeTokenRequest;

use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

use support\Log;

class AliImm
{

    public static function createClient($accessKeyId, $accessKeySecret, $endpoint)
    {
        $config = new Config([
            "accessKeyId" => $accessKeyId,
            "accessKeySecret" => $accessKeySecret
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/imm
        $config->endpoint = $endpoint;
        return new Imm($config);
    }

    /**
     * @param string[] $args
     * @return
     */
    public static function getToken($config, $fileName, $fileUrl, $readonly)
    {
        $client = self::createClient($config['oss_access_key'], $config['oss_access_secret'], $config['imm_endpoint']);
        $permissions = new WebofficePermission();
        $permissions->readonly = $readonly;
        $generateWebofficeTokenRequest = new GenerateWebofficeTokenRequest();
        $generateWebofficeTokenRequest->projectName = $config['imm_pro_name'];
        $generateWebofficeTokenRequest->filename = $fileName;
        $generateWebofficeTokenRequest->sourceURI = 'oss://' . $config['oss_bucket'] . '/' . $fileUrl;
        $generateWebofficeTokenRequest->permission = $permissions;

        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $res = $client->generateWebofficeTokenWithOptions($generateWebofficeTokenRequest, $runtime);
            return ['token' => $res->body->accessToken, 'refresh_token' => $res->body->refreshToken, 'url' => $res->body->webofficeURL];
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            Log::error('aliyun 文档预览 get token失败 ' . $error->message . $error->getTraceAsString());
            //Utils::assertAsString($error->message);
            return [];
        }
    }

    //refresh token
    public static function refreshToken($config, $token, $refreshToken)
    {
        $client = self::createClient($config['oss_access_key'], $config['oss_access_secret'], $config['imm_endpoint']);
        $generateWebofficeTokenRequest = new RefreshWebofficeTokenRequest();
        $generateWebofficeTokenRequest->projectName = $config['imm_pro_name'];
        $generateWebofficeTokenRequest->accessToken = $token;
        $generateWebofficeTokenRequest->refreshToken = $refreshToken;
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $res = $client->refreshWebofficeTokenWithOptions($generateWebofficeTokenRequest, $runtime);
            return ['token' => $res->body->accessToken, 'refresh_token' => $res->body->refreshToken];
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            Log::error('aliyun 文档预览 refresh token失败 ' . $error->message . $error->getTraceAsString());
            //Utils::assertAsString($error->message);
            return [];
        }
    }
}