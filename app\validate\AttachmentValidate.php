<?php

namespace app\validate;

use think\Validate;

class AttachmentValidate extends Validate
{
    protected $failException = true;

    /**
     * 验证规则
     */
    protected $rule = [
        'id' => 'require',
        'classify' => 'between:1,5',
        'classify_id' => 'requireCallback:classify_id_require|integer',
        'name' =>'requireWithout:id|max:255',
        'url' =>'max:255',
        'type' =>'requireWithout:id',
    ];

    /**
     * 提示消息
     */
    protected $message = [
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['classify', 'name', 'url', 'type', 'classify_id'],
        'edit'  => ['classify', 'name', 'url', 'type', 'classify_id'],
        'del' => ['id'],
        'detail' => ['id'],
    ];

    function classify_id_require($value, $data){
        if(!empty($data['classify']) && in_array($data['classify'], [1,2])){
            return true;
        }
    }

}