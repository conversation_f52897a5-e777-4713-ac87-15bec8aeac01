<?php

namespace library;

class tool
{
    public static function uuid()
    {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // 设置版本号为 4
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // 设置变体为 10
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * 将平级列表构建成多级树
     * @param array $items 平级数据列表
     * @param string $idKey 主键字段名
     * @param string $parentKey 父级ID字段名
     * @param string $childrenKey 子节点字段名
     * @param mixed $rootParentId 根节点的父ID值
     * @return array 树形结构
     */
    public static function buildTree(
        array  $items,
        string $idKey = 'id',
        string $parentKey = 'parent_id',
        string $childrenKey = 'children',
               $rootParentId = 0
    ): array
    {
        // 按父节点ID分组
        $grouped = [];
        foreach ($items as $item) {
            $grouped[$item[$parentKey]][] = $item;
        }

        // 递归构建树
        $build = function ($parentId) use (&$build, $grouped, $idKey, $childrenKey) {
            $tree = [];

            if (isset($grouped[$parentId])) {
                foreach ($grouped[$parentId] as $item) {
                    if ($children = $build($item[$idKey])) {
                        $item[$childrenKey] = $children;
                    }
                    $tree[] = $item;
                }
            }

            return $tree;
        };

        return $build($rootParentId);
    }

    /**
     * 通过id从树中遍历得到该节点和该节点的所有父节点
     * @param mixed $id 节点id
     * @param array $tree 树形结构
     * @param string $idKey 主键字段名
     * @param string $parentKey 父级ID字段名
     * @param string $childrenKey 子节点字段名
     * @param mixed $rootParentId 根节点的父ID值
     * @return array 从上到下组成的节点数组
     */
    public static function findPathFromTree(
        $id,
        array $tree,
        string $idKey = 'id',
        string $parentKey = 'parent_id',
        string $childrenKey = 'children',
        $rootParentId = 0
    ): array
    {
        $path = [];

        // 递归查找路径
        $find = function ($nodes) use ($id, $idKey, $parentKey, $childrenKey, &$find, &$path) {
            foreach ($nodes as $node) {
                if ($node[$idKey] == $id) {
                    array_unshift($path, $node);
                    return true;
                }

                if (isset($node[$childrenKey]) && is_array($node[$childrenKey])) {
                    if ($find($node[$childrenKey])) {
                        array_unshift($path, $node);
                        return true;
                    }
                }
            }
            return false;
        };

        $find($tree);
        return $path;
    }

    /**
     * 生成随机字符串
     * @param int $length 随机字符串长度
     * @param string $charset 字符集，默认包含大小写字母和数字
     * @return string 生成的随机字符串
     */
    public static function randomString(int $length = 16, string $charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string
    {
        if ($length <= 0) {
            throw new \InvalidArgumentException('长度必须大于0');
        }

        $result = '';
        $charsetLength = strlen($charset);

        for ($i = 0; $i < $length; $i++) {
            $randomIndex = random_int(0, $charsetLength - 1); // 使用安全的随机数生成器
            $result .= $charset[$randomIndex];
        }

        return $result;
    }

    /**
     * AES 加密 (CBC 模式 + PKCS7 填充)
     * @param string $data 明文数据
     * @param string $key 加密密钥（推荐32字节长度）
     * @param string $iv 初始化向量（16字节长度）
     * @return string|false base64编码的加密结果或失败
     */
    public static function aesEncrypt(string $data, string $key, string $iv): string
    {
        // 参数校验
        if (empty($data) || empty($key) || empty($iv)) {
            throw new \InvalidArgumentException('参数不能为空');
        }
        if (!extension_loaded('openssl')) {
            throw new \RuntimeException('需要开启 OpenSSL 扩展');
        }

        // 自动填充密钥和IV长度
        $key = substr(hash('sha256', $key), 0, 32);
        $iv = substr(hash('sha256', $iv), 0, 16);

        // 加密处理
        $encrypted = openssl_encrypt(
            $data,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return $encrypted ? base64_encode($encrypted) : false;
    }

    /**
     * AES 解密 (CBC 模式 + PKCS7 填充)
     * @param string $data base64编码的密文
     * @param string $key 加密密钥（需与加密时一致）
     * @param string $iv 初始化向量（需与加密时一致）
     * @return string|false 解密后的原始数据或失败
     */
    public static function aesDecrypt(string $data, string $key, string $iv): string
    {
        // 参数校验
        if (empty($data) || empty($key) || empty($iv)) {
            throw new \InvalidArgumentException('参数不能为空');
        }

        // 自动填充密钥和IV长度
        $key = substr(hash('sha256', $key), 0, 32);
        $iv = substr(hash('sha256', $iv), 0, 16);

        // 解密处理
        $decrypted = openssl_decrypt(
            base64_decode($data),
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return $decrypted;
    }

    public static function mimeToExtension($mimeType)
    {
        $mimeMap = [
            // 文档类
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
            'application/vnd.ms-excel' => 'xls',
            'text/plain' => 'txt',
            'text/markdown' => 'md',
            'text/csv' => 'csv',
            'application/json' => 'json',
            'application/xml' => 'xml',
            'text/html' => 'html',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'application/pdf' => 'pdf',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
            'application/vnd.ms-powerpoint' => 'ppt',
            'application/msword' => 'doc',

            // 图片类
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/svg+xml' => 'svg',
            'image/tiff' => 'tiff',
            'image/bmp' => 'bmp',

            // 视频类
            'video/mp4' => 'mp4',
            'video/x-matroska' => 'mkv',
            'video/quicktime' => 'mov',
            'video/x-msvideo' => 'avi',
            'video/x-flv' => 'flv',
            'video/webm' => 'webm',

            // 音频类
            'audio/mpeg' => 'mp3',
            'audio/wav' => 'wav',
            'audio/aac' => 'aac',
            'audio/ogg' => 'ogg',
            'audio/flac' => 'flac',
            'audio/x-m4a' => 'm4a',

            // 压缩包类
            'application/zip' => 'zip',
            'application/x-rar-compressed' => 'rar',
            'application/x-7z-compressed' => '7z',
            'application/x-tar' => 'tar',
            'application/x-gzip' => 'gz',

            // 可执行文件类
            'application/x-msdownload' => 'exe',
            'application/x-apple-diskimage' => 'dmg',
            'application/x-sh' => 'sh',

            // 其他常见类型
            'application/octet-stream' => 'bin',
            'application/x-bittorrent' => 'torrent',
            'application/vnd.android.package-archive' => 'apk',
            'application/x-iso9660-image' => 'iso',
        ];

        return $mimeMap[$mimeType] ?? null;
    }

    public static function formatDate($y, $m, $d)
    {
        return $y && $m && $d ? $y . '-' . str_pad((string)$m, 2, '0', STR_PAD_LEFT) . '-' . str_pad((string)$d, 2, '0', STR_PAD_LEFT) : '';
    }

    public static function getYmdFromDate($date)
    {
        if ($date == '0000-00-00') {
            return [0, 0, 0];
        }
        return explode('-', date('Y-m-d', strtotime($date)));
    }

    public static function fileExtToLower($filename)
    {
        $info = pathinfo($filename);
        if (!empty($info['extension'])) {
            $extension = strtolower($info['extension']);
            return $info['filename'] . '.' . $extension;
        }
        return $filename;
    }

    //两个时间间隔大于5分钟才展示时间
    public static function genChatTime($start, $end)
    {
        if (empty($start)) {
            return static::formatChatTime($end);
        }
        //间隔5分钟才展示时间
        if (($end - $start) > 300) {
            return static::formatChatTime($end);
        }
        return '';
    }

    /**
     * 格式化聊天列表时间
     * @param $timestamp number 时间戳
     * @return false|string
     */
    public static function formatChatTime($timestamp)
    {
        $today = strtotime('today');
        $yesterday = strtotime('yesterday');
        // 本周一
        $thisMonday = $today - ((date('w', time()) == 0 ? 7 : date('w', time())) - 1) * 24 * 3600;
        if ($timestamp > $today) {
            $result = date('H:i', $timestamp);
        } else if ($timestamp > $yesterday) {
            $result = '昨天 ' . date('H:i', $timestamp);
        } else if ($timestamp > $thisMonday) {
            $result = self::getWeekDesc($timestamp) . ' ' . date('H:i', $timestamp);
        } else {
            if (date('Y', $timestamp) == date('Y', time())) {
                $result = date('m月d日 H:i', $timestamp);
            } else {
                $result = date('Y年m月d日 H:i', $timestamp);
            }
        }
        return $result;
    }

    /**
     * 获取指定时间戳的星期几-中文描述
     * @param int $timeStamp 时间戳
     * @return string
     */
    public static function getWeekDesc($timeStamp)
    {
        if (intval($timeStamp) == 0) {
            return '';
        }
        $week = date('w', $timeStamp);
        switch ($week) {
            case 0:
                $desc = '星期日';
                break;
            case 1:
                $desc = '星期一';
                break;
            case 2:
                $desc = '星期二';
                break;
            case 3:
                $desc = '星期三';
                break;
            case 4:
                $desc = '星期四';
                break;
            case 5:
                $desc = '星期五';
                break;
            case 6:
                $desc = '星期六';
                break;
            default:
                $desc = '';
                break;
        }
        return $desc;
    }
}