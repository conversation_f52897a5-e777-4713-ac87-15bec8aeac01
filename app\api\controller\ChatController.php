<?php

namespace app\api\controller;

use app\api\service\FeatGuidanceService;
use app\controller\BaseController;
use app\model\MainAgentSessions;
use app\model\TaskResult;
use app\model\UserChat;
use library\Alioss;
use library\tool;
use Respect\Validation\Validator as v;
use support\Db;
use support\Log;
use support\Request;
use Webman\Exception\BusinessException;

class ChatController extends BaseController
{
    protected $indexFields = ['id', 'content', 'content_type', 'attachment_id', 'task_id', 'is_robot', 'is_like', 'is_dislike', 'dislike_reason', 'created_at'];
    protected $indexFilterFields = [
        'session_id' => '=',
    ];
    protected $indexSort = [
        'id' => 'desc',
    ];
    protected $attachmentModel;
    protected $ossUtil;

    public function __construct()
    {
        parent::__construct();
        $this->model = new UserChat();
        $this->attachmentModel = new \app\model\Attachment();
        $this->ossUtil = new Alioss;
    }

    protected function afterList(Request $request, array &$data)
    {
        $hideToolCall = $request->input('hide_tool_call', false);
        if (!empty($data['list'])) {
            $attachmentIds = array_filter(array_column($data['list'], 'attachment_id'));
            $attachmentMap = [];
            foreach ($data['list'] as $item) {
                if ($item['content_type'] == 'mix') {
                    $contentList = json_decode($item['content'], true);
                    foreach ($contentList as $contentItem) {
                        if (!empty($contentItem['attachment_id'])) {
                            $attachmentIds[] = $contentItem['attachment_id'];
                        }
                    }
                }
            }
            if (!empty($attachmentIds)) {
                $userId = getUserId();
                $attachmentMap = array_column($this->attachmentModel->getUserQuery($userId, ['id', 'name', 'type', 'url', 'size'])
                    ->whereIn('id', $attachmentIds)
                    ->get()
                    ->toArray(), null, 'id');
            }
            foreach ($data['list'] as $k => &$item) {
                if ($item['content_type'] == 'mix') {
                    $contentList = json_decode($item['content'], true);
                    foreach ($contentList as &$contentItem) {
                        if (!empty($contentItem['attachment_id'])) {
                            // $contentItem['content'] = $this->ossUtil->genGetSign($contentItem['content'])['sign_url'];
                            $contentItem['attachment'] = $attachmentMap[$contentItem['attachment_id']] ?? null;
                        }
                    }
                    $item['content'] = json_encode($contentList, JSON_UNESCAPED_UNICODE);
                }
                if ($item['is_robot']) {
                    // $item['content'] = str_replace("\\n", "<br>", $item['content']);
                    if ($hideToolCall) {
                        $contentList = json_decode($item['content'], true);
                        $item['content'] = end($contentList)['content'] ?? '';
                    }
                }
                //格式化时间
                $item['created_time'] = "";
                if ($k > 0) {
                    $item['created_time'] = tool::genChatTime(strtotime($item['created_at']), strtotime($data['list'][$k - 1]['created_at']));
                }
            }
        }
    }

    // get 功能引导list
    public function guidance(Request $request)
    {
        return $this->success(FeatGuidanceService::getGuidance());
    }

    /**
     * 通过ID或taskId删除消息
     * 同时删除UserChat和MainAgentSessions中的相关记录
     */
    public function del(Request $request)
    {
        $data = $request->all();

        $userId = getUserId();
        $messageId = $data['id'] ?? '';
        $taskId = $data['task_id'] ?? '';
        if (empty($messageId) && empty($taskId)) {
            return $this->error('参数错误,id和task_id不能同时为空');
        }

        // 获取要删除的消息信息
        if ($taskId) {
            $taskId = TaskResult::getOneByTaskId($userId, $taskId, ['id']);
            if (empty($taskId)) {
                return $this->error('任务id不存在');
            }
            $taskId = $taskId['id'];
        }
        $query = UserChat::getUserQuery($userId, ['id', 'user_id', 'session_id', 'content', 'is_robot', 'created_at']);
        if ($messageId) {
            $query->where('id', $messageId);
        } else {
            $query->where('task_id', $taskId);
        }
        $message = $query->first();

        if (empty($message)) {
            return $this->error('消息不存在或无权限删除');
        }
        $msgIds = [$message['id']];
        $aiContent = $message['is_robot'] ? $message['content'] : '';

        // 获取这组对话的另一条AI消息或用户消息
        $anotherMessage = UserChat::getUserQuery($userId, ['id', 'user_id', 'session_id', 'content', 'is_robot', 'created_at'])
            ->where('session_id', $message['session_id'])
            ->where('id', $message['is_robot'] ? '<' : '>', $message['id'])
            ->orderBy('id', $message['is_robot'] ? 'desc' : 'asc')
            ->first();
        if ($anotherMessage) {
            // 两条都是AI消息说明这条消息是推送消息不用从memory删除
            if ($message['is_robot'] && $anotherMessage['is_robot'] == $message['is_robot']) {
                $aiContent = '';
            } else {
                $msgIds[] = $anotherMessage['id'];
                $anotherMessage['is_robot'] && $aiContent = $anotherMessage['content'];
            }
        }

        // 开启事务确保数据一致性
        \support\Db::transaction(function () use ($message, $msgIds, $aiContent, $userId) {
            // 删除UserChat中的消息记录
            UserChat::whereIn('id', $msgIds)
                ->where('user_id', $userId)
                ->delete();

            // 从MainAgentSessions的memory中删除对应的对话
            if (!empty($message['session_id']) && !empty($aiContent)) {
                $createdAt = strtotime($message['created_at']);
                MainAgentSessions::removeMessageFromMemoryByContent(
                    $message['session_id'],
                    UserChat::extractTextFromJson($aiContent),
                    $createdAt,
                );
            }
        });

        return $this->success(['ids' => $msgIds]);
    }

    public function save(Request $request)
    {
        $post = $request->post();
        if (!empty($post['task_id'])) {
            $msg = UserChat::getMsgByTaskId(getUserId(), $post['task_id'], ['id']);
            if (empty($msg)) {
                return $this->error('消息不存在');
            }
            unset($post['task_id']);
            $request->setPost(array_merge($post, ['id' => $msg['id']]));
        }
        return parent::save($request);
    }
}