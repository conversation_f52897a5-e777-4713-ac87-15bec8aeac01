{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "8878172e56eb8f16dfb0689096cef596", "packages": [{"name": "adbario/php-dot-notation", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "shasum": ""}, "require": {"ext-json": "*", "php": "^5.5 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7|^6.6|^7.5|^8.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "support": {"issues": "https://github.com/adbario/php-dot-notation/issues", "source": "https://github.com/adbario/php-dot-notation/tree/2.5.0"}, "time": "2022-10-14T20:31:46+00:00"}, {"name": "alibabacloud/client", "version": "1.5.32", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "5bc6f6d660797dcee2c3aef29700ab41ee764f4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/5bc6f6d660797dcee2c3aef29700ab41ee764f4d", "reference": "5bc6f6d660797dcee2c3aef29700ab41ee764f4d", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4.1", "clagiordano/weblibs-configmanager": "^1.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "mtdowling/jmespath.php": "^2.5", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7|^6.6|^7.5|^8.5|^9.5", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"AlibabaCloud\\Client\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/openapi-sdk-php-client/issues", "source": "https://github.com/aliyun/openapi-sdk-php-client"}, "time": "2022-12-09T04:05:55+00:00"}, {"name": "alibabacloud/credentials", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/aliyun/credentials-php.git", "reference": "410338b1831f7547a40071af3a9adea27b2fe6e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/credentials-php/zipball/410338b1831f7547a40071af3a9adea27b2fe6e5", "reference": "410338b1831f7547a40071af3a9adea27b2fe6e5", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7|^6.6|^9.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Credentials for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "credentials", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/credentials-php/issues", "source": "https://github.com/aliyun/credentials-php"}, "time": "2025-03-04T07:38:54+00:00"}, {"name": "alibabacloud/darabonba", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba.git", "reference": "36f0b4191e73f8069527af7af1436bab29188ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba/zipball/36f0b4191e73f8069527af7af1436bab29188ebc", "reference": "36f0b4191e73f8069527af7af1436bab29188ebc", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4", "alibabacloud/tea": "^3.2", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "monolog/monolog": "^1.0|^2.1", "php": ">=5.5", "psr/http-message": "^0.11.0|^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3|^9.3", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Dara\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Darabonba for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "time": "2025-01-15T06:04:25+00:00"}, {"name": "alibabacloud/darabonba-openapi", "version": "0.2.13", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba-openapi.git", "reference": "0213396384e2c064eefd614f3dd53636a63f987f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba-openapi/zipball/0213396384e2c064eefd614f3dd53636a63f987f", "reference": "0213396384e2c064eefd614f3dd53636a63f987f", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.1", "alibabacloud/gateway-spi": "^1", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.21", "alibabacloud/tea-xml": "^0.2", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client", "support": {"issues": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/issues", "source": "https://github.com/alibabacloud-sdk-php/darabonba-openapi/tree/0.2.13"}, "time": "2024-07-15T13:11:36+00:00"}, {"name": "alibabacloud/dm-20151123", "version": "1.2.5", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/dm-20151123.git", "reference": "f3c01fa5505d09d073316ddafdcc73bb070f030e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/dm-20151123/zipball/f3c01fa5505d09d073316ddafdcc73bb070f030e", "reference": "f3c01fa5505d09d073316ddafdcc73bb070f030e", "shasum": ""}, "require": {"alibabacloud/darabonba": "^1.0.0", "alibabacloud/openapi-core": "^1.0.0", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Dm\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Dm (20151123) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/dm-20151123/tree/1.2.5"}, "time": "2025-05-26T17:21:20+00:00"}, {"name": "alibabacloud/dysmsapi-20170525", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/Dysmsapi-20170525.git", "reference": "5a301a076b29449a488607cbfbb7f12b8cc50c1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/Dysmsapi-20170525/zipball/5a301a076b29449a488607cbfbb7f12b8cc50c1f", "reference": "5a301a076b29449a488607cbfbb7f12b8cc50c1f", "shasum": ""}, "require": {"alibabacloud/darabonba-openapi": "^0.2.13", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.21", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Dysmsapi\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Dysmsapi (20170525) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/Dysmsapi-20170525/tree/3.1.1"}, "time": "2025-01-03T17:14:56+00:00"}, {"name": "alibabacloud/endpoint-util", "version": "0.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/endpoint-util.git", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/endpoint-util/zipball/f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "shasum": ""}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Endpoint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Endpoint Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/endpoint-util/tree/0.1.1"}, "time": "2020-06-04T10:57:15+00:00"}, {"name": "alibabacloud/gateway-spi", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi.git", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/alibabacloud-gateway-spi/zipball/7440f77750c329d8ab252db1d1d967314ccd1fcb", "reference": "7440f77750c329d8ab252db1d1d967314ccd1fcb", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\GatewaySpi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Gateway SPI Client", "support": {"source": "https://github.com/alibabacloud-sdk-php/alibabacloud-gateway-spi/tree/1.0.0"}, "time": "2022-07-14T05:31:35+00:00"}, {"name": "alibabacloud/imm-20200930", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/imm-20200930.git", "reference": "77d9904314c473188f8e7c0bca3e43825899492f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/imm-20200930/zipball/77d9904314c473188f8e7c0bca3e43825899492f", "reference": "77d9904314c473188f8e7c0bca3e43825899492f", "shasum": ""}, "require": {"alibabacloud/darabonba-openapi": "^0.2.10", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.10|^0.2.1", "alibabacloud/tea-utils": "^0.2.19", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Imm\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Intelligent Media Management (20200930) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/imm-20200930/tree/4.4.0"}, "time": "2024-03-19T17:23:46+00:00"}, {"name": "alibabacloud/openapi-core", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-core.git", "reference": "fbc9a40279de711e9010073a8b8c6edfb97f5cff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-core/zipball/fbc9a40279de711e9010073a8b8c6edfb97f5cff", "reference": "fbc9a40279de711e9010073a8b8c6edfb97f5cff", "shasum": ""}, "require": {"alibabacloud/credentials": "^1.2.2", "alibabacloud/darabonba": "^1", "alibabacloud/gateway-spi": "^1", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3|^9.3", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client Core", "support": {"issues": "https://github.com/alibabacloud-sdk-php/openapi-core/issues", "source": "https://github.com/alibabacloud-sdk-php/openapi-core/tree/v1.0.6"}, "time": "2025-05-06T06:06:38+00:00"}, {"name": "alibabacloud/openapi-util", "version": "0.2.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-util.git", "reference": "f31f7bcd835e08ca24b6b8ba33637eb4eceb093a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-util/zipball/f31f7bcd835e08ca24b6b8ba33637eb4eceb093a", "reference": "f31f7bcd835e08ca24b6b8ba33637eb4eceb093a", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2", "lizhichao/one-sm": "^1.5", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\OpenApiUtil\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi <PERSON>", "support": {"issues": "https://github.com/alibabacloud-sdk-php/openapi-util/issues", "source": "https://github.com/alibabacloud-sdk-php/openapi-util/tree/0.2.1"}, "time": "2023-01-10T09:10:10+00:00"}, {"name": "alibabacloud/sdk", "version": "1.8.2268", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php.git", "reference": "2f580518ac75f41eb48b0795933bb239cf842df3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php/zipball/2f580518ac75f41eb48b0795933bb239cf842df3", "reference": "2f580518ac75f41eb48b0795933bb239cf842df3", "shasum": ""}, "require": {"alibabacloud/client": "^1.5", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "php": ">=5.5"}, "replace": {"alibabacloud/aas": "self.version", "alibabacloud/actiontrail": "self.version", "alibabacloud/adb": "self.version", "alibabacloud/aegis": "self.version", "alibabacloud/afs": "self.version", "alibabacloud/airec": "self.version", "alibabacloud/alidns": "self.version", "alibabacloud/alikafka": "self.version", "alibabacloud/alimt": "self.version", "alibabacloud/aliprobe": "self.version", "alibabacloud/aliyuncvc": "self.version", "alibabacloud/appmallsservice": "self.version", "alibabacloud/arms": "self.version", "alibabacloud/arms4finance": "self.version", "alibabacloud/baas": "self.version", "alibabacloud/batchcompute": "self.version", "alibabacloud/bss": "self.version", "alibabacloud/bssopenapi": "self.version", "alibabacloud/cas": "self.version", "alibabacloud/cbn": "self.version", "alibabacloud/ccc": "self.version", "alibabacloud/ccs": "self.version", "alibabacloud/cdn": "self.version", "alibabacloud/cds": "self.version", "alibabacloud/cf": "self.version", "alibabacloud/chatbot": "self.version", "alibabacloud/cloudapi": "self.version", "alibabacloud/cloudauth": "self.version", "alibabacloud/cloudesl": "self.version", "alibabacloud/cloudmarketing": "self.version", "alibabacloud/cloudphoto": "self.version", "alibabacloud/cloudwf": "self.version", "alibabacloud/cms": "self.version", "alibabacloud/commondriver": "self.version", "alibabacloud/companyreg": "self.version", "alibabacloud/cr": "self.version", "alibabacloud/crm": "self.version", "alibabacloud/cs": "self.version", "alibabacloud/csb": "self.version", "alibabacloud/cusanalyticsconline": "self.version", "alibabacloud/dataworkspublic": "self.version", "alibabacloud/dbs": "self.version", "alibabacloud/dcdn": "self.version", "alibabacloud/dds": "self.version", "alibabacloud/democenter": "self.version", "alibabacloud/dm": "self.version", "alibabacloud/dmsenterprise": "self.version", "alibabacloud/domain": "self.version", "alibabacloud/domainintl": "self.version", "alibabacloud/drcloud": "self.version", "alibabacloud/drds": "self.version", "alibabacloud/dts": "self.version", "alibabacloud/dybaseapi": "self.version", "alibabacloud/dyplsapi": "self.version", "alibabacloud/dypnsapi": "self.version", "alibabacloud/dysmsapi": "self.version", "alibabacloud/dyvmsapi": "self.version", "alibabacloud/eci": "self.version", "alibabacloud/ecs": "self.version", "alibabacloud/ecsinc": "self.version", "alibabacloud/edas": "self.version", "alibabacloud/ehpc": "self.version", "alibabacloud/elasticsearch": "self.version", "alibabacloud/emr": "self.version", "alibabacloud/ess": "self.version", "alibabacloud/facebody": "self.version", "alibabacloud/fnf": "self.version", "alibabacloud/foas": "self.version", "alibabacloud/ft": "self.version", "alibabacloud/goodstech": "self.version", "alibabacloud/gpdb": "self.version", "alibabacloud/green": "self.version", "alibabacloud/hbase": "self.version", "alibabacloud/hiknoengine": "self.version", "alibabacloud/hpc": "self.version", "alibabacloud/hsm": "self.version", "alibabacloud/httpdns": "self.version", "alibabacloud/idst": "self.version", "alibabacloud/imageaudit": "self.version", "alibabacloud/imageenhan": "self.version", "alibabacloud/imagerecog": "self.version", "alibabacloud/imagesearch": "self.version", "alibabacloud/imageseg": "self.version", "alibabacloud/imm": "self.version", "alibabacloud/industrybrain": "self.version", "alibabacloud/iot": "self.version", "alibabacloud/iqa": "self.version", "alibabacloud/itaas": "self.version", "alibabacloud/ivision": "self.version", "alibabacloud/ivpd": "self.version", "alibabacloud/jaq": "self.version", "alibabacloud/jarvis": "self.version", "alibabacloud/jarvispublic": "self.version", "alibabacloud/kms": "self.version", "alibabacloud/linkedmall": "self.version", "alibabacloud/linkface": "self.version", "alibabacloud/linkwan": "self.version", "alibabacloud/live": "self.version", "alibabacloud/lubancloud": "self.version", "alibabacloud/lubanruler": "self.version", "alibabacloud/market": "self.version", "alibabacloud/mopen": "self.version", "alibabacloud/mpserverless": "self.version", "alibabacloud/mts": "self.version", "alibabacloud/multimediaai": "self.version", "alibabacloud/nas": "self.version", "alibabacloud/netana": "self.version", "alibabacloud/nlp": "self.version", "alibabacloud/nlpautoml": "self.version", "alibabacloud/nlscloudmeta": "self.version", "alibabacloud/nlsfiletrans": "self.version", "alibabacloud/objectdet": "self.version", "alibabacloud/ocr": "self.version", "alibabacloud/ocs": "self.version", "alibabacloud/oms": "self.version", "alibabacloud/ons": "self.version", "alibabacloud/onsmqtt": "self.version", "alibabacloud/oos": "self.version", "alibabacloud/openanalytics": "self.version", "alibabacloud/ossadmin": "self.version", "alibabacloud/ots": "self.version", "alibabacloud/outboundbot": "self.version", "alibabacloud/petadata": "self.version", "alibabacloud/polardb": "self.version", "alibabacloud/productcatalog": "self.version", "alibabacloud/pts": "self.version", "alibabacloud/push": "self.version", "alibabacloud/pvtz": "self.version", "alibabacloud/qualitycheck": "self.version", "alibabacloud/ram": "self.version", "alibabacloud/rds": "self.version", "alibabacloud/reid": "self.version", "alibabacloud/retailcloud": "self.version", "alibabacloud/rkvstore": "self.version", "alibabacloud/ros": "self.version", "alibabacloud/rtc": "self.version", "alibabacloud/saf": "self.version", "alibabacloud/sas": "self.version", "alibabacloud/sasapi": "self.version", "alibabacloud/scdn": "self.version", "alibabacloud/schedulerx2": "self.version", "alibabacloud/skyeye": "self.version", "alibabacloud/slb": "self.version", "alibabacloud/smartag": "self.version", "alibabacloud/smc": "self.version", "alibabacloud/sms": "self.version", "alibabacloud/smsintl": "self.version", "alibabacloud/snsuapi": "self.version", "alibabacloud/sts": "self.version", "alibabacloud/taginner": "self.version", "alibabacloud/tesladam": "self.version", "alibabacloud/teslamaxcompute": "self.version", "alibabacloud/teslastream": "self.version", "alibabacloud/ubsms": "self.version", "alibabacloud/ubsmsinner": "self.version", "alibabacloud/uis": "self.version", "alibabacloud/unimkt": "self.version", "alibabacloud/visionai": "self.version", "alibabacloud/vod": "self.version", "alibabacloud/voicenavigator": "self.version", "alibabacloud/vpc": "self.version", "alibabacloud/vs": "self.version", "alibabacloud/wafopenapi": "self.version", "alibabacloud/welfareinner": "self.version", "alibabacloud/xspace": "self.version", "alibabacloud/xtrace": "self.version", "alibabacloud/yqbridge": "self.version", "alibabacloud/yundun": "self.version"}, "require-dev": {"composer/composer": "^1.8", "league/climate": "^3.2.4", "phpunit/phpunit": "^4.8", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud SDK for PHP - Easier to Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "cloud", "library", "sdk"], "support": {"issues": "https://github.com/aliyun/openapi-sdk-php/issues", "source": "https://github.com/aliyun/openapi-sdk-php"}, "time": "2025-05-06T09:46:02+00:00"}, {"name": "alibabacloud/tea", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/1619cb96c158384f72b873e1f85de8b299c9c367", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "time": "2023-05-16T06:43:41+00:00"}, {"name": "alibabacloud/tea-utils", "version": "0.2.21", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-utils.git", "reference": "5039e45714c6456186d267f5d81a4b260a652495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-utils/zipball/5039e45714c6456186d267f5d81a4b260a652495", "reference": "5039e45714c6456186d267f5d81a4b260a652495", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\Utils\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea Utils for PHP", "support": {"issues": "https://github.com/aliyun/tea-util/issues", "source": "https://github.com/aliyun/tea-util"}, "time": "2024-07-05T06:05:54+00:00"}, {"name": "alibabacloud/tea-xml", "version": "0.2.4", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-xml.git", "reference": "3e0c000bf536224eebbac913c371bef174c0a16a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-xml/zipball/3e0c000bf536224eebbac913c371bef174c0a16a", "reference": "3e0c000bf536224eebbac913c371bef174c0a16a", "shasum": ""}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/var-dumper": "*"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\XML\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea XML Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/tea-xml/tree/0.2.4"}, "time": "2022-08-02T04:12:58+00:00"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5", "reference": "483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"php-coveralls/php-coveralls": "*", "phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.7.2"}, "time": "2024-10-28T10:41:12+00:00"}, {"name": "brick/math", "version": "0.12.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-02-28T13:11:00+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "5c8ebcc62782313b1278afe802b120d18c07a059"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/5c8ebcc62782313b1278afe802b120d18c07a059", "reference": "5c8ebcc62782313b1278afe802b120d18c07a059", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "support": {"issues": "https://github.com/clagiordano/weblibs-configmanager/issues", "source": "https://github.com/clagiordano/weblibs-configmanager/tree/v1.2.0"}, "time": "2021-05-18T17:55:57+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "fruitcake/php-cors", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/fruitcake/php-cors.git", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b", "shasum": ""}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "homepage": "https://github.com/fruitcake/php-cors", "keywords": ["cors", "laravel", "symfony"], "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-10-12T05:21:21+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/uri-template.git", "reference": "30e286560c137526eccd4ce21b2de477ab0676d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/uri-template/zipball/30e286560c137526eccd4ce21b2de477ab0676d2", "reference": "30e286560c137526eccd4ce21b2de477ab0676d2", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "time": "2025-02-03T10:55:03+00:00"}, {"name": "illuminate/bus", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/bus.git", "reference": "5bbb523f1317395ce9e9ded59d69d04a714b081d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/bus/zipball/5bbb523f1317395ce9e9ded59d69d04a714b081d", "reference": "5bbb523f1317395ce9e9ded59d69d04a714b081d", "shasum": ""}, "require": {"illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/pipeline": "^12.0", "illuminate/support": "^12.0", "php": "^8.2"}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^12.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/collections", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/collections.git", "reference": "f1d8ae882c014673b1f7bedab6435989553776e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/f1d8ae882c014673b1f7bedab6435989553776e3", "reference": "f1d8ae882c014673b1f7bedab6435989553776e3", "shasum": ""}, "require": {"illuminate/conditionable": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "php": "^8.2"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^7.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-04-03T17:58:47+00:00"}, {"name": "illuminate/conditionable", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/conditionable.git", "reference": "251ef166c6ee46cc8a141403253f83fe7ee50507"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/conditionable/zipball/251ef166c6ee46cc8a141403253f83fe7ee50507", "reference": "251ef166c6ee46cc8a141403253f83fe7ee50507", "shasum": ""}, "require": {"php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/container", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "cdd99204b29acf795a02f778542df5a5244361fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/cdd99204b29acf795a02f778542df5a5244361fd", "reference": "cdd99204b29acf795a02f778542df5a5244361fd", "shasum": ""}, "require": {"illuminate/contracts": "^12.0", "php": "^8.2", "psr/container": "^1.1.1|^2.0.1"}, "provide": {"psr/container-implementation": "1.1|2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-22T17:41:27+00:00"}, {"name": "illuminate/contracts", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "bbaec083da240396f2186f4c3a9952da207f28a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/bbaec083da240396f2186f4c3a9952da207f28a0", "reference": "bbaec083da240396f2186f4c3a9952da207f28a0", "shasum": ""}, "require": {"php": "^8.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/database", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "f03c9825a2ec701a214762d2455a530ba854125d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/f03c9825a2ec701a214762d2455a530ba854125d", "reference": "f03c9825a2ec701a214762d2455a530ba854125d", "shasum": ""}, "require": {"brick/math": "^0.11|^0.12", "ext-pdo": "*", "illuminate/collections": "^12.0", "illuminate/container": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "laravel/serializable-closure": "^1.3|^2.0", "php": "^8.2"}, "suggest": {"ext-filter": "Required to use the Postgres database driver.", "fakerphp/faker": "Required to use the eloquent factory builder (^1.24).", "illuminate/console": "Required to use the database commands (^12.0).", "illuminate/events": "Required to use the observers with Eloquent (^12.0).", "illuminate/filesystem": "Required to use the migrations (^12.0).", "illuminate/pagination": "Required to paginate the result set (^12.0).", "symfony/finder": "Required to use Eloquent model factories (^7.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-04-03T12:55:52+00:00"}, {"name": "illuminate/events", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "80fc4cdfef9c33a59c998d2eb361eb6ae7c124bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/80fc4cdfef9c33a59c998d2eb361eb6ae7c124bf", "reference": "80fc4cdfef9c33a59c998d2eb361eb6ae7c124bf", "shasum": ""}, "require": {"illuminate/bus": "^12.0", "illuminate/collections": "^12.0", "illuminate/container": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Events\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/filesystem", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "4fa2995bad173c1a0067b285bb7fef47a4b441c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/4fa2995bad173c1a0067b285bb7fef47a4b441c6", "reference": "4fa2995bad173c1a0067b285bb7fef47a4b441c6", "shasum": ""}, "require": {"illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "php": "^8.2", "symfony/finder": "^7.2.0"}, "suggest": {"ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-hash": "Required to use the Filesystem class.", "illuminate/http": "Required for handling uploaded files (^12.0).", "league/flysystem": "Required to use the Flysystem local driver (^3.25.1).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.25.1).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.25.1).", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.25.1).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^7.2).", "symfony/mime": "Required to enable support for guessing extensions (^7.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/http", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/http.git", "reference": "23140773e07bfc84396a5aec70796d269b00aa62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/http/zipball/23140773e07bfc84396a5aec70796d269b00aa62", "reference": "23140773e07bfc84396a5aec70796d269b00aa62", "shasum": ""}, "require": {"ext-filter": "*", "fruitcake/php-cors": "^1.3", "guzzlehttp/guzzle": "^7.8.2", "guzzlehttp/uri-template": "^1.0", "illuminate/collections": "^12.0", "illuminate/macroable": "^12.0", "illuminate/session": "^12.0", "illuminate/support": "^12.0", "php": "^8.2", "symfony/http-foundation": "^7.2.0", "symfony/http-kernel": "^7.2.0", "symfony/mime": "^7.2.0", "symfony/polyfill-php83": "^1.31"}, "suggest": {"ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image()."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Http\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Http package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-04-03T14:58:53+00:00"}, {"name": "illuminate/macroable", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/macroable.git", "reference": "e862e5648ee34004fa56046b746f490dfa86c613"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/e862e5648ee34004fa56046b746f490dfa86c613", "reference": "e862e5648ee34004fa56046b746f490dfa86c613", "shasum": ""}, "require": {"php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2024-07-23T16:31:01+00:00"}, {"name": "illuminate/pagination", "version": "v12.4.0", "source": {"type": "git", "url": "https://github.com/illuminate/pagination.git", "reference": "b2c972af37b033e1f02309d8e1ab1926777cfbdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pagination/zipball/b2c972af37b033e1f02309d8e1ab1926777cfbdf", "reference": "b2c972af37b033e1f02309d8e1ab1926777cfbdf", "shasum": ""}, "require": {"ext-filter": "*", "illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/support": "^12.0", "php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pagination\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pagination package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/pipeline", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "3c6df979e7e8ef09183a610cc0383179c1aba309"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/3c6df979e7e8ef09183a610cc0383179c1aba309", "reference": "3c6df979e7e8ef09183a610cc0383179c1aba309", "shasum": ""}, "require": {"illuminate/contracts": "^12.0", "illuminate/support": "^12.0", "php": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/redis", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/redis.git", "reference": "3457cbaea4583a2488bbea4999e62b2095493f24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/redis/zipball/3457cbaea4583a2488bbea4999e62b2095493f24", "reference": "3457cbaea4583a2488bbea4999e62b2095493f24", "shasum": ""}, "require": {"illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "php": "^8.2"}, "suggest": {"ext-redis": "Required to use the php<PERSON>is connector (^4.0|^5.0|^6.0).", "predis/predis": "Required to use the predis connector (^2.3)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Redis\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Redis package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/session", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/session.git", "reference": "54f55139d2a535681305f4d6027730b1b96cc7f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/session/zipball/54f55139d2a535681305f4d6027730b1b96cc7f1", "reference": "54f55139d2a535681305f4d6027730b1b96cc7f1", "shasum": ""}, "require": {"ext-ctype": "*", "ext-session": "*", "illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/filesystem": "^12.0", "illuminate/support": "^12.0", "php": "^8.2", "symfony/finder": "^7.2.0", "symfony/http-foundation": "^7.2.0"}, "suggest": {"illuminate/console": "Required to use the session:table command (^12.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Session\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Session package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-19T20:10:05+00:00"}, {"name": "illuminate/support", "version": "v12.7.2", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "1d5d07534bb2d9f16e655c2cb99607517ad43e8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/1d5d07534bb2d9f16e655c2cb99607517ad43e8f", "reference": "1d5d07534bb2d9f16e655c2cb99607517ad43e8f", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "illuminate/collections": "^12.0", "illuminate/conditionable": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "nesbot/carbon": "^3.8.4", "php": "^8.2", "voku/portable-ascii": "^2.0.2"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"spatie/once": "*"}, "suggest": {"illuminate/filesystem": "Required to use the Composer class (^12.0).", "laravel/serializable-closure": "Required to use the once function (^1.3|^2.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.6).", "league/uri": "Required to use the Uri class (^7.5.1).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the Composer class (^7.2).", "symfony/uid": "Required to use Str::ulid() (^7.2).", "symfony/var-dumper": "Required to use the dd function (^7.2).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.6.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-04-02T19:35:42+00:00"}, {"name": "laravel/serializable-closure", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "b352cf0534aa1ae6b4d825d1e762e35d43f8a841"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/b352cf0534aa1ae6b4d825d1e762e35d43f8a841", "reference": "b352cf0534aa1ae6b4d825d1e762e35d43f8a841", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"illuminate/support": "^10.0|^11.0|^12.0", "nesbot/carbon": "^2.67|^3.0", "pestphp/pest": "^2.36|^3.0", "phpstan/phpstan": "^2.0", "symfony/var-dumper": "^6.2.0|^7.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2025-03-19T13:51:03+00:00"}, {"name": "lizhichao/one-sm", "version": "1.10", "source": {"type": "git", "url": "https://github.com/lizhichao/sm.git", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lizhichao/sm/zipball/687a012a44a5bfd4d9143a0234e1060543be455a", "reference": "687a012a44a5bfd4d9143a0234e1060543be455a", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"OneSm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "国密sm3", "keywords": ["php", "sm3"], "support": {"issues": "https://github.com/lizhichao/sm/issues", "source": "https://github.com/lizhichao/sm/tree/1.10"}, "funding": [{"url": "https://www.vicsdf.com/img/w.jpg", "type": "custom"}, {"url": "https://www.vicsdf.com/img/z.jpg", "type": "custom"}], "time": "2021-05-26T06:19:22+00:00"}, {"name": "monolog/monolog", "version": "2.10.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5cf826f2991858b54d5c3809bee745560a1042a7", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.10.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-11-12T12:43:37+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.8.0"}, "time": "2024-09-04T18:46:31+00:00"}, {"name": "nesbot/carbon", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "6d16a8a015166fe54e22c042e0805c5363aef50d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/6d16a8a015166fe54e22c042e0805c5363aef50d", "reference": "6d16a8a015166fe54e22c042e0805c5363aef50d", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.57.2", "kylekatarnls/multi-tester": "^2.5.3", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.11.2", "phpunit/phpunit": "^10.5.20", "squizlabs/php_codesniffer": "^3.9.0"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-03-27T12:57:33+00:00"}, {"name": "nikic/fast-route", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/181d480e08d9476e61381e04a71b34dc0432e812", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "time": "2018-02-13T20:26:39+00:00"}, {"name": "overtrue/chinese-calendar", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/overtrue/chinese-calendar.git", "reference": "d78e86d66a4b225defac6828bf1de42c4a9d4bdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/chinese-calendar/zipball/d78e86d66a4b225defac6828bf1de42c4a9d4bdf", "reference": "d78e86d66a4b225defac6828bf1de42c4a9d4bdf", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Overtrue\\ChineseCalendar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "中国农历转换与查询工具", "keywords": ["Chinese", "calendar", "lunar", "lunar2solar", "solar", "solar2lunar"], "support": {"issues": "https://github.com/overtrue/chinese-calendar/issues", "source": "https://github.com/overtrue/chinese-calendar/tree/master"}, "time": "2020-03-09T07:21:26+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "respect/stringifier", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/Respect/Stringifier.git", "reference": "e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Respect/Stringifier/zipball/e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59", "reference": "e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.8", "malukenho/docheader": "^0.1.7", "phpunit/phpunit": "^6.4"}, "type": "library", "autoload": {"files": ["src/stringify.php"], "psr-4": {"Respect\\Stringifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Respect/Stringifier Contributors", "homepage": "https://github.com/Respect/Stringifier/graphs/contributors"}], "description": "Converts any value to a string", "homepage": "http://respect.github.io/Stringifier/", "keywords": ["respect", "stringifier", "stringify"], "support": {"issues": "https://github.com/Respect/Stringifier/issues", "source": "https://github.com/Respect/Stringifier/tree/0.2.0"}, "time": "2017-12-29T19:39:25+00:00"}, {"name": "symfony/clock", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/console", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:09:28+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/error-handler", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b", "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^6.4|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T07:12:39+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/finder", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:17+00:00"}, {"name": "symfony/http-foundation", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "371272aeb6286f8135e028ca535f8e4d6f114126"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/371272aeb6286f8135e028ca535f8e4d6f114126", "reference": "371272aeb6286f8135e028ca535f8e4d6f114126", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-25T15:54:33+00:00"}, {"name": "symfony/http-kernel", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "b1fe91bc1fa454a806d3f98db4ba826eb9941a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/b1fe91bc1fa454a806d3f98db4ba826eb9941a54", "reference": "b1fe91bc1fa454a806d3f98db4ba826eb9941a54", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/cache": "<6.4", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/translation": "<6.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.4", "twig/twig": "<3.12"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^7.1", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^7.1", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-28T13:32:50+00:00"}, {"name": "symfony/mime", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "87ca22046b78c3feaff04b337f33b38510fd686b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/87ca22046b78c3feaff04b337f33b38510fd686b", "reference": "87ca22046b78c3feaff04b337f33b38510fd686b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-19T08:51:20+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:18:16+00:00"}, {"name": "symfony/translation", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "283856e6981286cc0d800b53bd5703e8e363f05a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/283856e6981286cc0d800b53bd5703e8e363f05a", "reference": "283856e6981286cc0d800b53bd5703e8e363f05a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-13T10:27:23+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/var-dumper", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "82b478c69745d8878eb60f9a049a4d584996f73a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/82b478c69745d8878eb60f9a049a4d584996f73a", "reference": "82b478c69745d8878eb60f9a049a4d584996f73a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T11:39:41+00:00"}, {"name": "tencentcloud/asr", "version": "3.0.1360", "source": {"type": "git", "url": "https://github.com/tencentcloud-sdk-php/asr.git", "reference": "ec1d43a61939f3f4f3e6f70a3666650c00b7dd76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentcloud-sdk-php/asr/zipball/ec1d43a61939f3f4f3e6f70a3666650c00b7dd76", "reference": "ec1d43a61939f3f4f3e6f70a3666650c00b7dd76", "shasum": ""}, "require": {"tencentcloud/common": "3.0.1360"}, "type": "library", "autoload": {"psr-4": {"TencentCloud\\": "./src/TencentCloud"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "ten<PERSON>clouda<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/tencentcloud-sdk-php/asr", "role": "Developer"}], "description": "TencentCloudApi php sdk asr", "homepage": "https://github.com/tencentcloud-sdk-php/asr", "support": {"issues": "https://github.com/tencentcloud-sdk-php/asr/issues", "source": "https://github.com/tencentcloud-sdk-php/asr/tree/3.0.1360"}, "time": "2025-04-14T20:13:51+00:00"}, {"name": "tencentcloud/common", "version": "3.0.1360", "source": {"type": "git", "url": "https://github.com/tencentcloud-sdk-php/common.git", "reference": "b92aa526f61f01485d9097bd676ae2613cd93007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentcloud-sdk-php/common/zipball/b92aa526f61f01485d9097bd676ae2613cd93007", "reference": "b92aa526f61f01485d9097bd676ae2613cd93007", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3||^7.0", "php": ">=5.6.0"}, "type": "library", "autoload": {"psr-4": {"TencentCloud\\": "./src/TencentCloud"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "ten<PERSON>clouda<PERSON>", "email": "<EMAIL>", "homepage": "https://cloud.tencent.com/document/sdk/PHP", "role": "Developer"}], "description": "TencentCloudApi php sdk", "homepage": "https://github.com/tencentcloud-sdk-php/common", "support": {"issues": "https://github.com/tencentcloud-sdk-php/common/issues", "source": "https://github.com/tencentcloud-sdk-php/common/tree/3.0.1360"}, "time": "2025-04-14T20:32:45+00:00"}, {"name": "topthink/think-container", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/top-think/think-container.git", "reference": "a24d442a02fb2a4716de232ff1a4f006c178a370"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-container/zipball/a24d442a02fb2a4716de232ff1a4f006c178a370", "reference": "a24d442a02fb2a4716de232ff1a4f006c178a370", "shasum": ""}, "require": {"php": ">=8.0", "psr/container": "^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "PHP Container & Facade Manager", "support": {"issues": "https://github.com/top-think/think-container/issues", "source": "https://github.com/top-think/think-container/tree/v3.0.1"}, "time": "2025-01-07T08:19:23+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.11", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.11"}, "time": "2025-04-07T06:55:59+00:00"}, {"name": "topthink/think-validate", "version": "v3.0.5", "source": {"type": "git", "url": "https://github.com/top-think/think-validate.git", "reference": "f7dd85675270e9f8c0b04a13362133067629f53c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-validate/zipball/f7dd85675270e9f8c0b04a13362133067629f53c", "reference": "f7dd85675270e9f8c0b04a13362133067629f53c", "shasum": ""}, "require": {"php": ">=8.0", "topthink/think-container": ">=3.0"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think validate", "support": {"issues": "https://github.com/top-think/think-validate/issues", "source": "https://github.com/top-think/think-validate/tree/v3.0.5"}, "time": "2025-03-05T06:11:48+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2024-07-20T21:52:34+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.3"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2024-11-21T01:49:47+00:00"}, {"name": "webman/console", "version": "v2.1.7", "source": {"type": "git", "url": "https://github.com/webman-php/console.git", "reference": "709b47beb74cb1e7494b4b93a06d1996618bdacf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webman-php/console/zipball/709b47beb74cb1e7494b4b93a06d1996618bdacf", "reference": "709b47beb74cb1e7494b4b93a06d1996618bdacf", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "php": ">=8.1", "symfony/console": ">=6.0"}, "require-dev": {"workerman/webman": "^2.1"}, "type": "library", "autoload": {"psr-4": {"Webman\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "Webman console", "homepage": "http://www.workerman.net", "keywords": ["webman console"], "support": {"email": "<EMAIL>", "forum": "http://www.workerman.net/questions", "issues": "https://github.com/webman-php/console/issues", "source": "https://github.com/webman-php/console", "wiki": "http://www.workerman.net/doc/webman"}, "time": "2025-05-06T02:54:37+00:00"}, {"name": "webman/database", "version": "v2.1.6", "source": {"type": "git", "url": "https://github.com/webman-php/database.git", "reference": "943c5a0f92d8959d9160c2ee3925112b15c308c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webman-php/database/zipball/943c5a0f92d8959d9160c2ee3925112b15c308c8", "reference": "943c5a0f92d8959d9160c2ee3925112b15c308c8", "shasum": ""}, "require": {"illuminate/database": "^10.0 || ^11.0 || ^12.0", "illuminate/http": "^10.0 || ^11.0 || ^12.0", "laravel/serializable-closure": "^1.0 || ^2.0", "workerman/webman-framework": "^2.1 || dev-master"}, "type": "library", "autoload": {"psr-4": {"support\\": "src/support", "Webman\\Database\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Webman database", "support": {"issues": "https://github.com/webman-php/database/issues", "source": "https://github.com/webman-php/database/tree/v2.1.6"}, "time": "2025-04-04T13:23:25+00:00"}, {"name": "webman/rate-limiter", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/webman-php/rate-limiter.git", "reference": "f6ff79366ab56ac66bc507515d747cdef752db2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webman-php/rate-limiter/zipball/f6ff79366ab56ac66bc507515d747cdef752db2d", "reference": "f6ff79366ab56ac66bc507515d747cdef752db2d", "shasum": ""}, "require": {"php": ">=8.0", "workerman/webman-framework": ">=1.5.15"}, "type": "library", "autoload": {"psr-4": {"Webman\\RateLimiter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Webman plugin webman/rate-limiter", "support": {"issues": "https://github.com/webman-php/rate-limiter/issues", "source": "https://github.com/webman-php/rate-limiter/tree/v1.1.3"}, "time": "2024-12-27T10:02:44+00:00"}, {"name": "webman/redis", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/webman-php/redis.git", "reference": "559eb1692d39c6fef5cf526223fff728be6c0fb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webman-php/redis/zipball/559eb1692d39c6fef5cf526223fff728be6c0fb9", "reference": "559eb1692d39c6fef5cf526223fff728be6c0fb9", "shasum": ""}, "require": {"illuminate/redis": "^10.0 || ^11.0 || ^12.0", "workerman/webman-framework": "^2.1 || dev-master"}, "type": "library", "autoload": {"psr-4": {"support\\": "src/support", "Webman\\Redis\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "<PERSON>man redis", "support": {"issues": "https://github.com/webman-php/redis/issues", "source": "https://github.com/webman-php/redis/tree/v2.1.3"}, "time": "2025-03-14T03:52:14+00:00"}, {"name": "webman/redis-queue", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/webman-php/redis-queue.git", "reference": "80b9ddca0405bbb6d02e6b368e8036b3b1a13814"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webman-php/redis-queue/zipball/80b9ddca0405bbb6d02e6b368e8036b3b1a13814", "reference": "80b9ddca0405bbb6d02e6b368e8036b3b1a13814", "shasum": ""}, "require": {"workerman/redis-queue": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Webman\\RedisQueue\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "description": "Redis message queue plugin for webman.", "support": {"issues": "https://github.com/webman-php/redis-queue/issues", "source": "https://github.com/webman-php/redis-queue/tree/v1.3.2"}, "time": "2024-04-03T02:00:20+00:00"}, {"name": "workerman/coroutine", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/workerman-php/coroutine.git", "reference": "df8fc428967d512a74a8a7d80355c1d40228c9fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/workerman-php/coroutine/zipball/df8fc428967d512a74a8a7d80355c1d40228c9fa", "reference": "df8fc428967d512a74a8a7d80355c1d40228c9fa", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^11.0", "psr/log": "*"}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "src", "Workerman\\Coroutine\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Workerman coroutine", "support": {"issues": "https://github.com/workerman-php/coroutine/issues", "source": "https://github.com/workerman-php/coroutine/tree/v1.1.3"}, "time": "2025-02-17T03:34:21+00:00"}, {"name": "workerman/crontab", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/walkor/crontab.git", "reference": "74f51ca8204e8eb628e57bc0e640561d570da2cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/crontab/zipball/74f51ca8204e8eb628e57bc0e640561d570da2cb", "reference": "74f51ca8204e8eb628e57bc0e640561d570da2cb", "shasum": ""}, "require": {"php": ">=7.0", "workerman/workerman": ">=4.0.20"}, "type": "library", "autoload": {"psr-4": {"Workerman\\Crontab\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "A crontab written in PHP based on workerman", "homepage": "http://www.workerman.net", "keywords": ["crontab"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/crontab", "wiki": "http://doc.workerman.net/"}, "time": "2025-01-15T07:20:50+00:00"}, {"name": "workerman/redis", "version": "v2.0.5", "source": {"type": "git", "url": "https://github.com/walkor/redis.git", "reference": "49627c1809eff1ef7175eb8ee7549234a1d67ec5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/redis/zipball/49627c1809eff1ef7175eb8ee7549234a1d67ec5", "reference": "49627c1809eff1ef7175eb8ee7549234a1d67ec5", "shasum": ""}, "require": {"php": ">=7", "workerman/workerman": "^4.1.0||^5.0.0"}, "type": "library", "autoload": {"psr-4": {"Workerman\\Redis\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "support": {"issues": "https://github.com/walkor/redis/issues", "source": "https://github.com/walkor/redis/tree/v2.0.5"}, "time": "2025-04-07T01:58:58+00:00"}, {"name": "workerman/redis-queue", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/walkor/redis-queue.git", "reference": "75dbf7ed2ea228c45dc0df82c0fea35879b715d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/redis-queue/zipball/75dbf7ed2ea228c45dc0df82c0fea35879b715d0", "reference": "75dbf7ed2ea228c45dc0df82c0fea35879b715d0", "shasum": ""}, "require": {"php": ">=7.0", "workerman/redis": "^1.0||^2.0", "workerman/workerman": ">=4.0.20"}, "type": "library", "autoload": {"psr-4": {"Workerman\\RedisQueue\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message queue system written in PHP based on workerman and backed by Redis.", "homepage": "http://www.workerman.net", "support": {"issues": "https://github.com/walkor/redis-queue/issues", "source": "https://github.com/walkor/redis-queue/tree/v1.2.1"}, "time": "2025-01-02T09:21:45+00:00"}, {"name": "workerman/validation", "version": "v3.1.2", "source": {"type": "git", "url": "https://github.com/walkor/validation.git", "reference": "a4e9896e76b2fac92aff9a9f784df55f615571a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/validation/zipball/a4e9896e76b2fac92aff9a9f784df55f615571a0", "reference": "a4e9896e76b2fac92aff9a9f784df55f615571a0", "shasum": ""}, "require": {"php": "^8.0 || ^8.1 || ^8.2", "respect/stringifier": "^0.2.0", "symfony/polyfill-mbstring": "^1.2"}, "require-dev": {"egulias/email-validator": "^3.0", "giggsey/libphonenumber-for-php-lite": "^8.13", "malukenho/docheader": "^1.0", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.6", "psr/http-message": "^1.0", "respect/coding-standard": "^3.0", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"egulias/email-validator": "Improves the Email rule if available", "ext-bcmath": "Arbitrary Precision Mathematics", "ext-fileinfo": "File Information", "ext-mbstring": "Multibyte String Functions", "giggsey/libphonenumber-for-php-lite": "Enables the phone rule if available"}, "type": "library", "autoload": {"psr-4": {"Respect\\Validation\\": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Respect/Validation Contributors", "homepage": "https://github.com/Respect/Validation/graphs/contributors"}], "description": "The most awesome validation engine ever created for PHP. Respect/Validation 汉化版本", "homepage": "http://respect.github.io/Validation/", "keywords": ["respect", "validation", "validator"], "support": {"source": "https://github.com/walkor/validation/tree/v3.1.2"}, "time": "2023-12-07T06:19:04+00:00"}, {"name": "workerman/webman-framework", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/walkor/webman-framework.git", "reference": "f803bd867f07bb0929faef060b59a19a44186bfc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/webman-framework/zipball/f803bd867f07bb0929faef060b59a19a44186bfc", "reference": "f803bd867f07bb0929faef060b59a19a44186bfc", "shasum": ""}, "require": {"ext-json": "*", "nikic/fast-route": "^1.3", "php": ">=8.1", "psr/container": ">=1.0", "psr/log": "^3.0", "workerman/workerman": "^5.1 || dev-master"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"files": ["./src/support/helpers.php"], "psr-4": {"Webman\\": "./src", "Support\\": "./src/support", "support\\": "./src/support", "Support\\View\\": "./src/support/view", "Support\\Bootstrap\\": "./src/support/bootstrap", "Support\\Exception\\": "./src/support/exception"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "description": "High performance HTTP Service Framework.", "homepage": "https://www.workerman.net", "keywords": ["High Performance", "http service"], "support": {"email": "<EMAIL>", "forum": "https://wenda.workerman.net/", "issues": "https://github.com/walkor/webman/issues", "source": "https://github.com/walkor/webman-framework", "wiki": "https://doc.workerman.net/"}, "time": "2025-03-10T11:52:22+00:00"}, {"name": "workerman/workerman", "version": "v5.1.1", "source": {"type": "git", "url": "https://github.com/walkor/workerman.git", "reference": "72cafa19e488896abd2576ea67b3b1af1abf71c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman/zipball/72cafa19e488896abd2576ea67b3b1af1abf71c0", "reference": "72cafa19e488896abd2576ea67b3b1af1abf71c0", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "workerman/coroutine": "^1.1 || dev-main"}, "conflict": {"ext-swow": "<v1.0.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.0", "mockery/mockery": "^1.6", "pestphp/pest": "2.x-dev", "phpstan/phpstan": "1.11.x-dev"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "https://www.workerman.net", "keywords": ["asynchronous", "event-loop", "framework", "http"], "support": {"email": "<EMAIL>", "forum": "https://www.workerman.net/questions", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "https://www.workerman.net/doc/workerman/"}, "funding": [{"url": "https://opencollective.com/workerman", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "time": "2025-04-02T14:01:24+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.1"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}