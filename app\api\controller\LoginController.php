<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\InviteCode;
use app\model\User;
use app\model\UserChat;
use library\redisKeys;
use library\Sms;
use Respect\Validation\Validator as v;
use support\Redis;
use support\Request;

class LoginController extends BaseController
{

    protected $noNeedLogin = ['login', 'sendCode'];

    //login
    public function login(Request $request)
    {
        $data = $request->post();
        v::input($data, [
            'mobile' => v::number()->length(11, 11)->setName('手机号'),
            'code' => v::notEmpty()->length(6, 6)->setName('验证码'),
        ]);
        $key = redisKeys::USER_MOBILE_CODE . $data['mobile'];
        $code = Redis::get($key);
        if ($code != $data['code']) {
            if (in_array($data['mobile'], ["15837965917", "15219468292", "18659251788"]) && $data['code'] == "888999") {
                //白名单
            } else {
                return $this->error('验证码错误');
            }
        }

        //是否注册
        $user = User::getByMobile($data['mobile'], ['id', 'is_activated']);
        if (empty($user)) {
            $userId = User::addOne($data);
            UserChat::AddIntroduce($userId);
            $isActive = 0;
        } else {
            $userId = $user['id'];
            $isActive = $user['is_activated'];
            $user->fill($data)->save();
        }
        $token = User::genToken($userId);
        return $this->success(['token' => $token, 'is_active' => $isActive]);
    }


    //发送手机验证码
    public function sendCode(Request $request)
    {
        $data = v::input($request->post(), [
            'mobile' => v::number()->length(11, 11)->setName('手机号'),
        ]);
        $key = redisKeys::USER_MOBILE_CODE . $data['mobile'];
        $code = rand(100000, 999999);
        //发送验证码
        Sms::sendSms($data['mobile'], $code);
        Redis::setEx($key, 600, $code);
        return $this->success('发送成功');
    }

    //邀请码激活账号
    public function active(Request $request)
    {
        $data = v::input($request->post(), [
            'code' => v::notEmpty()->length(8, 8)->setName('激活码'),
        ]);
        $code = InviteCode::getByCode($data['code']);
        if (empty($code)) {
            return $this->error('激活码不存在');
        }
        if ($code['is_used'] == 1) {
            return $this->error('激活码已使用');
        }
        $data['code'] = strtoupper($data['code']);
        $uid = getUserId();
        InviteCode::used($data['code'], $uid);
        User::edit($uid, ['is_activated' => 1]);
        return $this->success('激活成功');
    }

}
