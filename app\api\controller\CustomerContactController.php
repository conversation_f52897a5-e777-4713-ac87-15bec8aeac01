<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\BaseModel;
use app\model\Customers;
use app\model\CustomersContact;
use app\validate\CustomerContactValidate;
use library\tool;
use support\Request;
use Webman\Exception\BusinessException;

class CustomerContactController extends BaseController
{
    protected $customerModel;

    public function __construct()
    {
        parent::__construct();
        $this->customerModel = new Customers();
        $this->model = new CustomersContact();
        $this->validate = new CustomerContactValidate();
    }

    public function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        $userId = getUserId();
        if (!empty($data['customer_id'])) {
            if (!$this->customerModel::getOne($userId, $data['customer_id'], ['customer_id'])) {
                throw new BusinessException('客户信息不存在');
            }
        }
        if (!empty($data['contact_birthday'])) {
            $ymd = tool::getYmdFromDate($data['contact_birthday']);
            $data['contact_birthday_y'] = $ymd[0];
            $data['contact_birthday_m'] = $ymd[1];
            $data['contact_birthday_d'] = $ymd[2];
        }
        return $model->fill($data)->save();
    }

    protected function afterDetail(Request $request, &$data)
    {
        $data['contact_birthday'] = tool::formatDate($data['contact_birthday_y'], $data['contact_birthday_m'], $data['contact_birthday_d']);
    }

    protected function afterList(Request $request, array &$data)
    {
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$item) {
                $item['contact_birthday'] = tool::formatDate($item['contact_birthday_y'], $item['contact_birthday_m'], $item['contact_birthday_d']);
            }
        }
    }
}