<?php

namespace app\validate;

use think\Validate;

class CustomerContactValidate extends Validate
{
    protected $failException = true;

    /**
     * 验证规则
     */
    protected $rule = [
        'id' => 'require',
        'customer_id' => 'require|integer',
        'contact_name' =>'require|max:100',
    ];

    /**
     * 提示消息
     */
    protected $message = [
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['customer_id', 'contact_name'],
        'del' => ['id'],
        'detail' => ['id'],
    ];

}