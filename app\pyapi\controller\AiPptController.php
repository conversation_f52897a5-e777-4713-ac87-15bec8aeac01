<?php

namespace app\pyapi\controller;

use app\model\UserPpt;
use app\pyapi\service\LinkService;
use library\aippt\Api;
use library\aippt\ApiV2;
use Respect\Validation\Validator as v;
use support\Log;
use support\Request;

class AiPptController extends BaseController
{

    public function create(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::number()->setName('用户ID'),
            'subject' => v::notEmpty()->setName('主题'),
            'content' => v::notEmpty()->setName('内容'),
        ]);
        //$data['content'] = $request->post('content', '');
        // 第三方用户ID（数据隔离）
        $uid = $data['user_id'];
        $subject = $data['subject'];

        // 创建 api token (有效期2小时，建议缓存到redis，同一个 uid 创建时之前的 token 会在10秒内失效)
        $apiToken = Api::getToken($uid);
        Log::info("api token: " . $apiToken . "\n");

        // 生成大纲
        //Log::info("\n\n========== 正在生成大纲 ==========\n");
        $outline = Api::generateOutline($apiToken, $subject, null, null);

        // 生成大纲内容
        //Log::info("\n\n========== 正在生成大纲内容 ==========\n");
        $markdown = Api::generateContent($apiToken, $outline, null, null);

        // 随机一个模板
        //Log::info("\n\n========== 随机选择模板 ==========\n");
        //$templateId = Api::randomOneTemplateId($apiToken);
        $templateId = $this->getRandTempId();
        //Log::info("templateId: " . $templateId . "\n");

        // 生成PPT
        //Log::info("\n\n========== 正在生成PPT ==========\n");
        $pptInfo = Api::generatePptx($apiToken, $templateId, $markdown, false);
        $pptId = $pptInfo["id"];
        //Log::info("\n" . "pptId: " . $pptId . "\n");
        //Log::info("ppt主题：" . $pptInfo["subject"] . "\n");
        //Log::info("ppt封面：" . $pptInfo["coverUrl"] . "?token=" . $apiToken . "\n");

        UserPpt::addOne([
            'user_id' => $uid,
            'ppt_id' => $pptId,
        ]);
        //$url = Api::downloadPptx($apiToken, $pptId);
        $linkInfo = LinkService::genLink($uid, LinkService::LINK_PPT_EDIT, $pptId);
        $url = $linkInfo['link'] ?? '';
        return $this->success([
            'pptId' => $pptId,
            "template_id" => $templateId,
            'url' => $url,
        ]);
    }

    public function updTemplate(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::number()->setName('用户ID'),
            'ppt_id' => v::notEmpty()->setName('用户ID'),
            'neq_ids' => v::notEmpty()->setName('模板IDs'),
        ]);
        $data['themeColor'] = $request->post('theme_color', ''); //主题颜色
        $data['style'] = $request->post('style', ''); //风格
        if ($data['style'] && !in_array($data['style'], ["扁平简约", "商务科技", "文艺清新", "卡通手绘", "中国风", "创意时尚", "创意趣味"])) {
            return $this->error("风格参数错误，风格可选的值：扁平简约,商务科技,文艺清新,卡通手绘,中国风,创意时尚,创意趣味");
        }
        if ($data['themeColor']) {
            $data['themeColor'] = $this->genColor($data['themeColor']);
        }
        $apiToken = Api::getToken($data['user_id']);
        $neqIds = $data['neq_ids'];
        //获取随机模板
        Log::info('style=' . $data['style'] . '   color=' . $data['themeColor']);
        $templateId = Api::randomOneTemplateId($apiToken, [$neqIds], $data['style'], $data['themeColor']);

        //更换模板
        $res = Api::updateTemplate($apiToken, $data["ppt_id"], $templateId);

        //$url = Api::downloadPptx($apiToken, $data["ppt_id"]);
        $linkInfo = LinkService::genLink($data['user_id'], LinkService::LINK_PPT_EDIT, $data["ppt_id"]);
        $url = $linkInfo['link'] ?? '';
        return $this->success([
            'templateId' => $templateId,
            'url' => $url,
        ]);
    }

    public function genColor($color)
    {
        return match ($color) {
            '橙色' => '#FA920A',
            '蓝色' => '#589AFD',
            '紫色' => '#7664FA',
            '青色' => '#65E5EC',
            '绿色' => '#61D328',
            '黄色' => '#F5FD59',
            '红色' => '#E05757',
            '棕色' => '#8F5A0B',
            '白色' => '#FFFFFF',
            '黑色' => '#000000',
            default => '',
        };
    }

    public function getRandTempId($filter = [])
    {
        $list = [1815576613715435520, 1792408621909413888, 1793540127126138880, 1795285075651452928, 1804898801006403584, 1804905770857521152, 1806290661188820992, 1806291734771261440,
            1806297030256222208, 1806303058985213952, 1806304212762746880, 1807601742553276416, 1807658348435464192, 1807662300296110080, 1807663800703508480, 1807668725705596928,
            1807686611094462464, 1807706873223438336, 1807981775423791104, 1808755362686558208, 1808796602845093888, 1808811167792226304, 1808828785563525120, 1808834259553869824,
            1809065702548824064, 1809105615784763392, 1809109294709465088, 1809138637657595904, 1809159952124862464, 1809187130824712192, 1809201240576614400, 1810158624325230592,
            1810196733502939136, 1810199294905671680, 1815272794641129472];
        if ($filter) {
            $list = array_diff($list, $filter);
        }
        return $list[array_rand($list)];
    }

    /**
     * V2 创建PPT（文本内容生成）
     * 简化版本，参考V1的create方法设计
     */
    public function createV2(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::number()->setName('用户ID'),
            'content' => v::notEmpty()->setName('内容'),
        ]);

        $data['type'] = $request->post('type', 6); // 默认文本内容生成
        $data['subject'] = $request->post('subject', ''); // 可选主题

        Log::info('PPT V2 create started', [
            'user_id' => $data['user_id'],
            'type' => $data['type'],
            'content_length' => mb_strlen($data['content'])
        ]);

        // 验证内容长度
        ApiV2::validateContentLength($data['type'], $data['content']);

        // 复用V1的token获取
        $apiToken = ApiV2::getToken($data['user_id']);
        Log::info("V2 api token: " . $apiToken);

        try {
            // 使用V2一键生成
            $options = ['lang' => 'zh'];
            if (!empty($data['subject'])) {
                $options['subject'] = $data['subject'];
            }

            $pptInfo = ApiV2::generatePptComplete(
                $apiToken,
                $data['type'],
                $data['content'],
                $options
            );

            $pptId = $pptInfo["id"] ?? null;
            if (!$pptId) {
                throw new \RuntimeException("PPT生成失败：未获取到PPT ID");
            }

            // 保存到数据库
            UserPpt::addOne([
                'user_id' => $data['user_id'],
                'ppt_id' => $pptId,
            ]);

            // 生成编辑链接
            $linkInfo = LinkService::genLink($data['user_id'], LinkService::LINK_PPT_EDIT, $pptId);
            $url = $linkInfo['link'] ?? '';

            Log::info('PPT V2 created successfully', [
                'user_id' => $data['user_id'],
                'ppt_id' => $pptId,
                'type' => $data['type']
            ]);

            return $this->success([
                'pptId' => $pptId,
                'taskId' => $pptInfo['taskId'] ?? null,
                'url' => $url,
                'type' => ApiV2::getTypeDescription($data['type'])
            ]);

        } catch (\Exception $e) {
            Log::error('PPT V2 create failed', [
                'operation' => 'create_ppt_v2',
                'user_id' => $data['user_id'],
                'type' => $data['type'],
                'error' => $e->getMessage()
            ]);
            return $this->error('PPT生成失败: ' . $e->getMessage());
        }
    }

    /**
     * V2 分步生成PPT（高级用法）
     */
    public function createTaskV2(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::number()->setName('用户ID'),
            'type' => v::intVal()->between(1, 7)->setName('生成类型'),
            'content' => v::optional(v::stringType())->setName('内容'),
        ]);

        Log::info('PPT V2 create task started', [
            'user_id' => $data['user_id'],
            'type' => $data['type']
        ]);

        // 验证内容长度
        if (!empty($data['content'])) {
            ApiV2::validateContentLength($data['type'], $data['content']);
        }

        $apiToken = ApiV2::getToken($data['user_id']);

        try {
            // 处理文件上传（如果有）
            $files = $this->handleFileUploadsV2($request);

            // 创建任务
            $taskId = ApiV2::createTask($apiToken, $data['type'], $data['content'], $files);

            Log::info('PPT V2 task created', [
                'user_id' => $data['user_id'],
                'task_id' => $taskId,
                'type' => $data['type']
            ]);

            return $this->success([
                'taskId' => $taskId,
                'type' => ApiV2::getTypeDescription($data['type'])
            ]);

        } catch (\Exception $e) {
            Log::error('PPT V2 create task failed', [
                'operation' => 'create_task_v2',
                'user_id' => $data['user_id'],
                'type' => $data['type'],
                'error' => $e->getMessage()
            ]);
            return $this->error('创建任务失败: ' . $e->getMessage());
        }
    }

    /**
     * V2 生成内容
     */
    public function generateContentV2(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::number()->setName('用户ID'),
            'task_id' => v::notEmpty()->setName('任务ID'),
        ]);

        $apiToken = ApiV2::getToken($data['user_id']);

        try {
            $options = ['lang' => $request->post('lang', 'zh')];
            $content = ApiV2::generateContent($apiToken, $data['task_id'], $options);

            return $this->success([
                'taskId' => $data['task_id'],
                'content' => $content
            ]);

        } catch (\Exception $e) {
            Log::error('PPT V2 generate content failed', [
                'operation' => 'generate_content_v2',
                'task_id' => $data['task_id'],
                'error' => $e->getMessage()
            ]);
            return $this->error('生成内容失败: ' . $e->getMessage());
        }
    }

    /**
     * V2 生成PPT文件
     */
    public function generatePptxV2(Request $request)
    {
        $data = v::input($request->post(), [
            'user_id' => v::number()->setName('用户ID'),
            'task_id' => v::notEmpty()->setName('任务ID'),
        ]);

        $apiToken = ApiV2::getToken($data['user_id']);

        try {
            $options = ['lang' => $request->post('lang', 'zh')];

            // 如果提供了markdown内容，直接使用
            $markdown = $request->post('markdown', '');
            if (!empty($markdown)) {
                $options['markdown'] = $markdown;
            } else {
                // 否则先生成内容
                $generatedContent = ApiV2::generateContent($apiToken, $data['task_id'], $options);
                $options['markdown'] = $generatedContent;
            }

            $pptInfo = ApiV2::generatePptx($apiToken, $data['task_id'], $options);

            $pptId = $pptInfo["id"] ?? null;
            if ($pptId) {
                // 保存到数据库
                UserPpt::addOne([
                    'user_id' => $data['user_id'],
                    'ppt_id' => $pptId,
                ]);

                // 生成编辑链接
                $linkInfo = LinkService::genLink($data['user_id'], LinkService::LINK_PPT_EDIT, $pptId);
                $pptInfo['url'] = $linkInfo['link'] ?? '';
            }

            return $this->success($pptInfo);

        } catch (\Exception $e) {
            Log::error('PPT V2 generate pptx failed', [
                'operation' => 'generate_pptx_v2',
                'task_id' => $data['task_id'],
                'error' => $e->getMessage()
            ]);
            return $this->error('生成PPT失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理文件上传（简化版）
     */
    private function handleFileUploadsV2(Request $request): array
    {
        $files = [];
        $uploadedFiles = $request->file();

        if (empty($uploadedFiles)) {
            return $files;
        }

        foreach ($uploadedFiles as $uploadedFile) {
            if (is_array($uploadedFile)) {
                foreach ($uploadedFile as $file) {
                    if ($file) {
                        $files[] = [
                            'path' => $file->getPathname(),
                            'name' => $file->getFilename(),
                        ];
                    }
                }
            } else {
                if ($uploadedFile) {
                    $files[] = [
                        'path' => $uploadedFile->getPathname(),
                        'name' => $uploadedFile->getFilename(),
                    ];
                }
            }
        }

        return $files;
    }

}