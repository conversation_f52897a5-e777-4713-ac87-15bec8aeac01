<?php

namespace library;

/**
 * id 整型数字对称加密，使用场景：对ID加密后展示
 */
class IdEncode
{
    // 密钥
    const key = 'reKLsHRkwBIciaj57MxzFGymVNtYQ2EJ8g4PDUpO1d69noCXqfZvW0SAul3hTb';
    const keyLen = 62;

    public static function encode($int)
    {
        //判断是否为整型
        if (!is_numeric($int)) {
            return '';
        }
        $int = intval(100000 + $int); //保证输出的加密字符长度至少是7位
        //将传入数字转换成十六进制分割成数组
        $hexArr = str_split(dechex($int));
        //将密钥分割成数组
        $keyArr = str_split(static::key);
        //密钥长度，推荐62
        $keyLen = static::keyLen;
        //随机数字
        $rand = mt_rand(0, $keyLen - 1);
        //将随机值压入结果开头
        $str = $keyArr[$rand];
        //验证码
        $verfy = $keyArr[($keyLen - $rand + strlen($int)) % $keyLen];
        //循环十六进制每一位数字，替换成密钥里的值
        foreach ($hexArr as $v) {
            $offset = hexdec($v) + $rand;
            $str .= $keyArr[$offset % $keyLen];
        }
        //将验证码压入结果末尾并返回
        return $str . $verfy;
    }

    public static function decode($str)
    {
        //验证$str是否合法
        if (!preg_match('/^[0-9a-zA-Z]{2,10}$/', $str)) {
            return '';
        }
        //将传入字符串分割成数组
        $strArr = str_split($str);
        //密钥
        $key = static::key;
        //将密钥分割成数组
        $keyArr = str_split(static::key);
        //密钥长度
        $keyLen = static::keyLen;
        //十六进制数值
        $hex = '';
        //获取随机数
        $rand = strpos($key, array_shift($strArr));
        //获取验证码
        $verfy = array_pop($strArr);
        //循环每一个字串并转换成十六进制
        foreach ($strArr as $k => $v) {
            if (strpos($key, $v) >= $rand) {
                $hex .= dechex(strpos($key, $v) - $rand);
            } else {
                $hex .= dechex($keyLen - $rand + strpos($key, $v));
            }
        }
        //十六进制转换成十进制
        $dec = hexdec($hex);
        //判断验证码是否正确
        if ($verfy !== $keyArr[($keyLen - $rand + strlen($dec)) % $keyLen]) {
            return '';
        }
        $dec = intval($dec - 100000);
        return $dec;
    }
}
