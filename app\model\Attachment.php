<?php

namespace app\model;

use app\api\service\AttachmentService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use library\Alioss;
use support\Model;
use Webman\Exception\BusinessException;

class Attachment extends BaseModel
{
    use SoftDeletes;

    protected $fillable = ['parent_id', 'user_id', 'classify', 'classify_id', 'name', 'type', 'url', 'size', 'vector_ids', 'file_hash'];
    protected $table = 'attachment';
    protected $primaryKey = 'id';
    public $timestamps = true;

    const CLASSIFY_CUSTOMER = 1; // 客户文件
    const CLASSIFY_BUSINESS = 2; // 商机文件
    const CLASSIFY_PRODUCT = 3; // 销售物料
    const CLASSIFY_PROJECT = 4; // 客户资料
    const CLASSIFY_OTHER = 5; // 其他
    const CLASSIFY_TEMP = 6; // 临时文件夹

    protected function url(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => $value ? (new Alioss)->genGetSign($value)['sign_url'] : '',
        );
    }

    /**
     * 在原有的list查询上拓展查询语句
     * 
     * @param array $data 请求参数
     * @param Model|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder &$query 原有的查询对象
     * @param $fields 要查询的字段
     */
    public static function makeListQuery($userId, array $data, Model|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder &$query, $fields)
    {
        if (!empty($data['type'])) {
            if ($data['type'] == 'file') {
                $query->where('type', '<>', 'dir'); // 只查询文件
            } else {
                $query->where('type', $data['type']); 
            }
        }
        $query->where('classify', '<>', Attachment::CLASSIFY_TEMP);
        $classify = $data['classify'] ?? 0;
        $customerIds = $businessIds = [];
        // 客户资料需查出所有客户文件和商机文件, 客户文件需查询出所有商机文件
        if ($classify == Attachment::CLASSIFY_PROJECT) {
            $customerIds = Customers::getUserQuery($userId)
                ->pluck('customer_id')
                ->toArray();
        } elseif ($classify == Attachment::CLASSIFY_CUSTOMER) {
            $customerIds = [$data['classify_id']];
        }
        if (in_array($classify, [Attachment::CLASSIFY_CUSTOMER, Attachment::CLASSIFY_PROJECT])) {
            $businessIds = BusinessOpportunities::getUserQuery($userId)
                ->whereIn('customer_id', $customerIds)
                ->pluck('bo_id')
                ->toArray();
        }
        if ($customerIds) { // 组合查询出所有客户文件
            $query->union(Attachment::select($fields)
                ->where('classify', Attachment::CLASSIFY_CUSTOMER)
                ->whereIn('classify_id', $customerIds));
        }
        if ($businessIds) { // 组合查询出客户下的所有商机文件
            $query->union(Attachment::select($fields)
                ->where('classify', Attachment::CLASSIFY_BUSINESS)
                ->whereIn('classify_id', $businessIds));
        }
    }

    public static function getByHash($hash, $userId = null, $fields = ['*'])
    {
        return self::getUserQuery($userId, $fields)->where('file_hash', $hash)->first();
    }

    /**
     * 获取分类id对应的根目录
     */
    public static function getClassifyRootDir($classify, $classifyId, $userId = null, $fields = ['*'])
    {
        return self::getUserQuery($userId, $fields)
            ->where('classify', $classify)
            ->where('classify_id', $classifyId)
            ->where('type', 'dir')
            ->first();
    }

    public static function delById($id, $userId = null)
    {
        !is_array($id) && $id = [$id];
        $list = self::getUserQuery($userId, ['vector_ids', 'id', 'type'])
            ->whereIn('id', $id)
            ->get()
            ->toArray();
        if (empty($list)) {
            throw new BusinessException('记录不存在'); 
        }
        $ids = $vectorIds = [];
        foreach ($list as $item) { // 找出所有文件id和向量id
            if ($item['type'] == 'dir') continue;
            $ids[] = $item['id'];
            $item['vector_ids'] && $vectorIds = array_merge($vectorIds, explode(',', $item['vector_ids']));
        }
        if ($vectorIds || $ids) { // 删除向量
            (new AttachmentService)->vectorDel($vectorIds, $userId, $ids);
        }
        return parent::delById($id, $userId);
    }

    public static function delByClassifyId($classify, $classifyId, $userId = null)
    {
        !is_array($classifyId) && $classifyId = [$classifyId];
        $query = self::getUserQuery($userId)
            ->where('classify', $classify)
            ->whereIn('classify_id', $classifyId);
        $list = (clone $query)->whereNot('type', 'dir')->get('vector_ids, id')->toArray();
        $ids = $vectorIds = [];
        foreach ($list as $item) { // 找出所有文件id和向量id
            $ids[] = $item['id'];
            $item['vector_ids'] && $vectorIds = array_merge($vectorIds, explode(',', $item['vector_ids']));
        }
        if ($vectorIds || $ids) { // 删除向量
            (new AttachmentService)->vectorDel($vectorIds, $userId, $ids);
        }
        return $query->delete();
    }
}