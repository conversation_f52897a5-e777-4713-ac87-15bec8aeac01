<?php

namespace library;

use GuzzleHttp\Client;

class WxRobotNotice
{

    public static function sendError($title, $content)
    {
        $webhook = getenv('WECHAT_ERROR_WEBHOOK');
        if (empty($webhook)) {
            return;
        }
        $client = new Client();
        return $client->request('POST', $webhook, [
            'json' => [
                'msgtype' => "markdown",
                'markdown' => [
                    "content" => "<font color='warning'>" . $title . "</font>\n>" . $content,
                ]
            ]
        ]);
    }

}