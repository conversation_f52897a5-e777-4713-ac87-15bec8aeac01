<?php

namespace app\pyapi\controller;

use app\model\Attachment;
use app\model\BaseModel;
use app\validate\AttachmentValidate;
use GuzzleHttp\Client;
use library\Alioss;
use library\tool;
use Respect\Validation\Validator as v;
use support\Db;
use support\Request;
use Webman\Exception\BusinessException;

/**
 * 附件管理
 */
class AttachmentController extends PyBaseController
{
    protected $indexFields = ['id', 'parent_id', 'classify', 'classify_id', 'name', 'type', 'url', 'size', 'created_at', 'updated_at'];
    protected $indexSortFields = ['id', 'name'];
    protected $indexFilterFields = [
        'classify' => '=',
        'classify_id' => '=',
        'name' => 'like',
    ];
    protected $indexSort = [
        'id' => 'desc',
    ];
    protected $ossUtil;
    protected $service;

    public function __construct()
    {
        parent::__construct();
        $this->model = new Attachment();
        $this->validate = new AttachmentValidate;
        $this->ossUtil = new Alioss;
        $this->service = new \app\api\service\AttachmentService;
    }

    protected function beforeList(Request $request, \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder &$query)
    {
        $data = $request->all();
        Attachment::makeListQuery(getUserId(), $data, $query, $this->indexFields);
    }

    protected function afterList(Request $request, array &$data)
    {
        foreach ($data['list'] as &$item) {
            $item = $item->toArray();
            $item['url'] && $item['url'] = $this->service->genViewUrlById($item['id'], $item['type']);
        }
        $data['list'] = tool::buildTree($data['list']);
    }

    protected function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        Db::transaction(function () use ($model, $data) {
            $oldData = $model->toArray();
            $model->fill($data)->save();
            $this->service->withMoveFile($oldData, $data);
        });
    }

}