<?php

namespace app\wxwork\service;

use app\model\MainAgentSessions;
use app\model\User;
use app\model\UserChat;
use app\model\UserWxwork;
use library\Voice;
use support\Log;

class WxUserService
{

    //是否注册
    public static function regis($appId, $userWxId)
    {
        $info = UserWxwork::getById($appId, $userWxId, ['*']);
        if (empty($info)) {
            UserWxwork::addOne([
                'app_id' => $appId,
                'third_id' => $userWxId,
            ]);
            return [];
        }
        return $info;
    }

    //注册
    public static function doRegis($info)
    {
        //是否已存在账号
        $user = User::getByMobile($info['mobile'], ['id']);
        if (empty($user)) {
            $userId = User::addOne([
                'mobile' => $info['mobile'],
            ]);
        } else {
            $userId = $user['id'];
        }
        UserWxwork::editById($info['id'], [
            'user_id' => $userId,
            'status' => UserWxwork::STATUS_SUCCESS,
        ]);

    }


    //将用户的语音文件转文本
    public static function transVoiceText($id, $mediaId, $format)
    {
        Log::info('transVoiceText, mediaid=' . $mediaId);
        $mediaFile = CoreService::getMedia($id, $mediaId);
        $name = time() . rand(100000, 999990) . '.' . $format;
        $path = base_path(('public/wxwork_media/' . $name));
        file_put_contents($path, $mediaFile);
        $url = env('APP_API') . '/wxwork_media/' . $name;
        return Voice::voiceToText($url, $format);
    }

    //get 文件信息
    public static function getMediaInfo($id, $mediaId)
    {
        $data = CoreService::getMediaFile($id, $mediaId);
        if (empty($data)) {
            return '';
        }
        Log::info(var_export($data['name'], true));
        $ext = static::getFileExt($data['name']);
        $name = time() . rand(100000, 999999) . '.' . $ext;
        $path = base_path(('public/wxwork_media/' . $name));
        file_put_contents($path, $data['content']);
        $url = env('APP_API') . '/wxwork_media/' . $name;
        return [
            'name' => $data['name'],
            'url' => $url,
            'size' => filesize($path),
        ];
    }

    // getFileExt
    public static function getFileExt($fileName)
    {
        return strtolower(substr(strrchr($fileName, '.'), 1));
    }

    public static function getSessionId($userId, bool $isNew = false)
    {
        return "s{$userId}-" . ($isNew ? time(): 0);
        //是否要重新生成session_id
        $lastMsg = UserChat::getLastMsg($userId, ['created_at']);
        if (empty($lastMsg)) {
            $sessionId = 's' . time();
        } else {
            //判断间隔时间是否超过60分钟
            Log::info('间隔时间:' . time() - strtotime($lastMsg['created_at']));
            if ((time() - strtotime($lastMsg['created_at'])) > 3600) {
                //新生成session_id
                $sessionId = 's' . time();
                Log::info('间隔时间超过60分钟，使用新的session_id');
            } else {
                //get 上一个sessionId
                Log::info('间隔时间未超过60分钟，使用旧的session_id');
                $sessionInfo = MainAgentSessions::getLastOne($userId, ['session_id']);
                if (empty($sessionInfo)) {
                    Log::info('旧的session_id 不存在');
                    $sessionId = 's' . time();
                } else {
                    $sessionId = $sessionInfo['session_id'];
                    Log::info('旧的session_id=' . $sessionId);
                }
            }
        }
        return $sessionId;
    }

}