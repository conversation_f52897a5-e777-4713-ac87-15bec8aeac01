<?php

namespace app\queue\redis\fast;
//错误告警

use library\Mail;
use support\Log;
use Webman\RedisQueue\Consumer;

class SendMail implements Consumer
{

    // 要消费的队列名
    public $queue = 'send_mail';

    public $connection = 'default';


    /**
     * 消费
     * data: alias, subject, content, $toAddress
     **/
    public function consume($data)
    {
        Mail::send($data['alias'], $data['subject'], $data['content'], $data['toAddress']);
    }

}