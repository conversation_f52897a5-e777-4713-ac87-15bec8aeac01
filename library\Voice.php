<?php

namespace library;

use support\Log;
use TencentCloud\Common\Credential;
use TencentCloud\Common\Profile\ClientProfile;
use TencentCloud\Common\Profile\HttpProfile;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Asr\V20190614\AsrClient;
use TencentCloud\Asr\V20190614\Models\SentenceRecognitionRequest;

class Voice
{

    const SecretId = 'AKIDoeVNG3jCCJMIFCHKJIC3mNm2NdjgXDnM';
    const SecretKey = 'ayEMWXQH6mIg641qW7pUFyfIoLn88CAA';

    public static function voiceToText($url, $voiceFormat = 'silk', $sourceType = 0)
    {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            $cred = new Credential(static::SecretId, static::SecretKey);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("asr.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            $client = new AsrClient($cred, "", $clientProfile);

            // 实例化一个请求对象,每个接口都会对应一个request对象
            $req = new SentenceRecognitionRequest();

            $params = array(
                "EngSerViceType" => "16k_zh-PY",
                //"EngSerViceType" => "16k_zh",
                "SourceType" => $sourceType,
                "Url" => $url,
                "VoiceFormat" => $voiceFormat, //wav、pcm、ogg-opus、speex、silk、mp3、m4a、aac, amr
            );
            $req->fromJsonString(json_encode($params));

            // 返回的resp是一个SentenceRecognitionResponse的实例，与请求对象对应
            $resp = $client->SentenceRecognition($req);
            $resp = $resp->toJsonString();
            $resp = json_decode($resp, true);
            if (empty($resp['Result'])) {
                return '';
            }
            return $resp['Result'];
        } catch (TencentCloudSDKException $e) {
            Log::error('语音转文字失败：url=【' . $url . '】 ' . $e);
            return '';
        }
    }

}