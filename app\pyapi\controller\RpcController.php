<?php
namespace app\pyapi\controller;

use support\Request;
use Respect\Validation\Validator as v;
use support\Log;

class RpcController extends BaseController
{
    public function index(Request $request)
    {
        $data = v::input($request->all(), [
            'class' => v::notEmpty()->setName('class'),
            'method' => v::notEmpty()->setName('method'),
            'params' => v::arrayVal()->setName('params'),
        ]);
        $class = $data['class'];
        $method = $data['method'];
        $params = $data['params']?? [];
        if (!class_exists($class)) {
            return $this->error("class {$class} not found");
        }
        if (!method_exists($class, $method)) {
            return $this->error("method {$method} not found");
        }
        try {
            $result = (new $class())->$method(...$params);
        } catch (\Throwable $e) {
            Log::error("python call rpc error: {$e->getMessage()}", ['trace' => $e->getTraceAsString(), 'data' => $data]);
            return $this->error($e->getMessage());
        }
        return $this->success($result);
    }
}