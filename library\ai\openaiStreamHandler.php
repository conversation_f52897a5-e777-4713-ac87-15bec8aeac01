<?php

namespace library\ai;

use library\redisKeys;
use support\Log;
use support\Redis;

class openaiStreamHandler
{

    private $data_buffer;//缓存，有可能一条data被切分成两部分了，无法解析json，所以需要把上一半缓存起来
    private $counter;//数据接收计数器
    private $chars;//字符数组，开启敏感词检测时用于缓存待检测字符
    private $punctuation;//停顿符号
    private $dfa = NULL;
    private $check_sensitive = FALSE;
    private $id = '';
    private $class = '';
    private $param = [];
    private $isEndReasoning = false;

    public function __construct($params)
    {
        $this->buffer = '';
        $this->counter = 0;
        $this->chars = [];
        $this->lines = [];
        $this->punctuation = ['，', '。', '；', '？', '！', '……'];
        $this->id = $params['id'] ?? '';
        $this->class = $params['class'] ?? '';
        $this->param = $params;
    }

    public function callback($ch, $data)
    {
        $this->counter += 1;
        $result = json_decode($data, TRUE);
        if (is_array($result)) {
            Log::error('ai 返回错误：' . json_encode($result));
            $this->write("非常抱歉，当前服务器繁忙，请稍后再试。感谢您的理解和支持！");
            $this->end();
            return "";
        }

        // 0、把上次缓冲区内数据拼接上本次的data
        $buffer = $this->data_buffer . $data;

        //拼接完之后，要把缓冲字符串清空
        $this->data_buffer = '';

        // 1、把所有的 'data: {' 替换为 '{' ，'data: [' 换成 '['
        $buffer = str_replace('data: {', '{', $buffer);
        $buffer = str_replace('data: [', '[', $buffer);

        // 2、把所有的 '}\n\n{' 替换维 '}[br]{' ， '}\n\n[' 替换为 '}[br]['
        $buffer = str_replace('}' . PHP_EOL . PHP_EOL . '{', '}[br]{', $buffer);
        $buffer = str_replace('}' . PHP_EOL . PHP_EOL . '[', '}[br][', $buffer);

        // 3、用 '[br]' 分割成多行数组
        $lines = explode('[br]', $buffer);

        // 4、循环处理每一行，对于最后一行需要判断是否是完整的json
        $line_c = count($lines);
        foreach ($lines as $li => $line) {
            if (trim($line) == '[DONE]') {
                //数据传输结束
                $this->data_buffer = '';
                $this->counter = 0;
                $this->sensitive_check();
                $this->end();
                break;
            }
            $line_data = json_decode(trim($line), TRUE);
            if (!is_array($line_data) || !isset($line_data['choices']) || !isset($line_data['choices'][0])) {
                if ($li == ($line_c - 1)) {
                    //如果是最后一行
                    $this->data_buffer = $line;
                    break;
                }
                //如果是中间行无法json解析，则写入错误日志中
                Log::error(json_encode(['i' => $this->counter, 'line' => $line, 'li' => $li], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL . PHP_EOL);
                continue;
            }

            if (isset($line_data['choices'][0]['delta']) && isset($line_data['choices'][0]['delta']['content'])) {
                $this->sensitive_check($line_data['choices'][0]['delta']['content']);
                if ($this->isEndReasoning == false && !empty($line_data['choices'][0]['delta']['content'])) {
                    //思考结束
                    $this->isEndReasoning = true;
                    $this->endReasoning();
                }
            }

            if (isset($line_data['choices'][0]['delta']) && isset($line_data['choices'][0]['delta']['reasoning_content'])) {
                $this->writeReason($line_data['choices'][0]['delta']['reasoning_content']);
            }
        }

        return strlen($data);
    }

    private function sensitive_check($content = NULL)
    {
        // 如果不检测敏感词，则直接返回给前端
        if (!$this->check_sensitive) {
            $this->write($content);
            return;
        }


        //每个 content 都检测是否包含换行或者停顿符号，如有，则成为一个新行
        if (!$this->has_pause($content)) {
            $this->chars[] = $content;
            return;
        }
        $this->chars[] = $content;
        $content = implode('', $this->chars);
        if ($this->dfa->containsSensitiveWords($content)) {
            $content = $this->dfa->replaceWords($content);
            $this->write($content);
        } else {
            foreach ($this->chars as $char) {
                $this->write($char);
            }
        }
        $this->chars = [];
    }

    private function has_pause($content)
    {
        if ($content == NULL) {
            return TRUE;
        }
        $has_p = false;
        if (is_numeric(strripos(json_encode($content), '\n'))) {
            $has_p = true;
        } else {
            foreach ($this->punctuation as $p) {
                if (is_numeric(strripos($content, $p))) {
                    $has_p = true;
                    break;
                }
            }
        }
        return $has_p;
    }

    private function write($content = NULL)
    {
        if ($content != NULL) {
            $key = RedisKeys::AI_RES . $this->id;
            $word = Redis::get($key) ?: "";
            $newWord = $word . $content;
            Redis::setex($key, 600, $newWord);
        }
    }

    //记录思考过程
    private function writeReason($content = '')
    {
        if ($content != "") {
            $key = RedisKeys::AI_RES_REASONING . $this->id;
            $word = Redis::get($key) ?: "";
            $newWord = $word . $content;
            Redis::setex($key, 600, $newWord);
        }
    }

    public function endReasoning()
    {
        $this->writeReason("#$");
    }

    public function end()
    {
        $this->write("#$");
        if ($this->class) {
            call_user_func([$this->class, 'saveResMessage'], $this->param);
        }
    }
}


