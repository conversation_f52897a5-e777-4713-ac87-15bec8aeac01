<?php

namespace app\wxwork\service;

use app\model\WxworkConfig;
use GuzzleHttp\Client;

use library\redisKeys;
use support\Log;
use support\Redis;

class CoreService
{
    const MSG_TYPE_TEXT = 'text';
    const MSG_TYPE_FILE = 'file';
    const MSG_TYPE_VOICE = 'voice';
    const MSG_TYPE_IMAGE = 'image';
    const MSG_TYPE_VIDEO = 'video';

    // get access_token
    public static function getAccessToken($id)
    {
        $key = redisKeys::WxWorkAccessToken . $id;
        if (Redis::exists($key)) {
            return Redis::get($key);
        }
        $info = WxworkConfig::getOne($id);
        if (empty($info)) {
            Log::error('企业微信应用 配置不存在 id=' . $id);
            throw new \Exception('操作异常');
        }

        $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" . $info['corpId'] . "&corpsecret=" . $info['corpsecret'];
        $res = file_get_contents($url);
        $resArr = json_decode($res, true);
        if ($resArr['errcode'] == 0) {
            Redis::setex(RedisKeys::WxWorkAccessToken . $id, $resArr['expires_in'], $resArr['access_token']);
            return $resArr['access_token'];
        } else {
            Log::error("企业微信获取access token失败");
            Log::error('企业微信获取access token失败=' . $res);
            throw new \Exception('操作异常');
        }
    }

    //发送应用消息
    public static function sendMsg($id, $toUser, $content, $msgType)
    {
        $token = static::getAccessToken($id);
        $url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" . $token;
        $info = WxWorkConfig::getOne($id);
        if (empty($info)) {
            return '';
        }
        switch ($msgType) {
            case static::MSG_TYPE_TEXT:
                //文本消息
                return static::sendTextMsg($toUser, $info['agentId'], $content, $url);
            case static::MSG_TYPE_FILE:
                break;
        }
    }

    //发送文本消息
    public static function sendTextMsg($toUser, $agentId, $content, $url)
    {
        $param = [
            'touser' => $toUser,
            'msgtype' => static::MSG_TYPE_TEXT,
            'agentid' => $agentId,
            'text' => ['content' => $content],
        ];
        $res = static::request('POST', $url, $param);
        if ($res['errcode'] != 0) {
            Log::error("企业微信发送文本消息失败");
            log::error(json_encode($res));
            return false;
        }
        return true;
    }

    public static function request($method, $api, $param, $timeout = 60)
    {
        $client = new Client();
        $response = $client->request($method, $api, $param ? [
            \GuzzleHttp\RequestOptions::JSON => $param,
            \GuzzleHttp\RequestOptions::TIMEOUT => $timeout
        ] : []);
        $res = $response->getBody()->getContents();
        return json_decode($res, true);
    }

    //get 临时媒体文件
    public static function getMedia($id, $mediaId)
    {
        $token = static::getAccessToken($id);
        $url = "https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=" . $token . "&media_id=" . $mediaId;
        return file_get_contents($url);
    }

    //get 临时媒体文件- 图片
    public static function getMediaFile($id, $mediaId)
    {
        $token = static::getAccessToken($id);
        $url = "https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=" . $token . "&media_id=" . $mediaId;

        $client = new Client();
        $response = $client->request("GET", $url);
        $header = $response->getHeader('Content-disposition');
        preg_match('/filename="(.+)"/', $header[0], $matches);
        $name = $matches[1];
        return [
            'name' => $name,
            'content' => $response->getBody()->getContents(),
        ];
    }

    //
    public static function genXmlMsg($senderId, $corpId, $agentID, $resp, $wxcpt)
    {
        //回复消息
        $sRespData = "<xml><ToUserName><![CDATA[" . $senderId . "]]></ToUserName><FromUserName><![CDATA[" . $corpId . "]]></FromUserName><CreateTime>" . time() . "</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[" . $resp . "]]></Content><AgentID>" . $agentID . "</AgentID></xml>";
        $sEncryptMsg = ""; //xml格式的密文
        $errCode = $wxcpt->EncryptMsg($sRespData, $sReqTimeStamp, $sReqNonce, $sEncryptMsg);
        if ($errCode == 0) {
            echo $sEncryptMsg;
        } else {
            Logger::error('加密返回消息失败' . $errCode);
            \Log::error('msg=' . $sRespData);
        }
    }
}
