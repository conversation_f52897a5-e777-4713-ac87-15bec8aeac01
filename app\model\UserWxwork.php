<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;
use support\Model;

class UserWxwork extends BaseModel
{
    use SoftDeletes;

    protected $table = 'user_wxwork';
    protected $primaryKey = 'id';
    public $timestamps = true;

    const STATUS_SEND = 1; //已发送验证码，等待验证
    const STATUS_SUCCESS = 2; //验证通过


    public static function getById($appId, $thirdId, $fields = ['*'])
    {
        return self::select($fields)
            ->where('app_id', $appId)
            ->where('third_id', $thirdId)
            ->first();
    }

    public static function getByUserIdAppId($appId, $userId, $fields = ['*'])
    {
        return self::select($fields)
            ->where('app_id', $appId)
            ->where('user_id', $userId)
            ->first();
    }

    public static function editById($id, $data)
    {
        return self::where('id', $id)
            ->update($data);
    }

    public static function editByThirdId($appId, $thirdId, $data)
    {
        return self::where('app_id', $appId)
            ->where('third_id', $thirdId)
            ->update($data);
    }


}