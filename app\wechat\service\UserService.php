<?php

namespace app\wechat\service;

use app\model\User;
use app\model\UserWx;
use app\model\UserWxwork;

class UserService
{

    public static function regis($appId, $thirdId)
    {
        $info = UserWx::getById($appId, $thirdId);
        if (empty($info)) {
            UserWx::addOne([
                'app_id' => $appId,
                'third_id' => $thirdId,
            ]);
            return [];
        }
        return $info;
    }

    //注册
    public static function doRegis($info)
    {
        //是否已存在账号
        $user = User::getByMobile($info['mobile'], ['id']);
        if (empty($user)) {
            $userId = User::addOne([
                'mobile' => $info['mobile'],
            ]);
        } else {
            $userId = $user['id'];
        }
        UserWx::editById($info['id'], [
            'user_id' => $userId,
            'status' => UserWx::STATUS_SUCCESS,
        ]);
    }

}