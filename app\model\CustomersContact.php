<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;

class CustomersContact extends BaseModel
{
    use SoftDeletes;


    protected $fillable = ['user_id', 'customer_id', 'contact_name', 'contact_remark', 'contact_title', 'email', 'phone', 'address', 'contact_birthday_y', 'contact_birthday_m', 'contact_birthday_d', 'contact_profile'];
    protected $table = 'customers_contact';
    protected $primaryKey = 'id';
    public $timestamps = true;

    public static function getAllByBirthday($m, $d, $y = null, $uid = null, $fields = ['*'])
    {
        $query = $uid? static::getUserQuery($uid, $fields) : static::select($fields);
        $y && $query->where('contact_birthday_y', $y);
        return $query->where('contact_birthday_m', $m)
            ->where('contact_birthday_d', $d)
            ->get()
            ->toArray();
    }
}