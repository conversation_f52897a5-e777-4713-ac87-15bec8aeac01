<?php

namespace app\api\controller;

use app\controller\BaseController;
use app\model\BaseModel;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use Respect\Validation\Validator as v;
use support\Db;
use support\Request;
use Webman\Exception\BusinessException;

class TodoController extends BaseController
{
    protected $indexFields = ["id", "title", "due_date", "priority", "customer_id", "bo_id", "is_finished", "sort_no", 'type', 'notify_time', 'notify_cycle'];
    protected $indexFilterFields = [
        'customer_id' => '=',
        'bo_id' => '=',
        'is_finished' => '=',
        'title' => 'like',
    ];
    protected $indexSort = [
        'sort_no' => 'asc',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->model = new TodoItems();
        $this->validateRules = [
            'add' => [
                'title' => v::notEmpty()->setName('标题'),
            ],
        ];
    }

    protected function saveOpt(Request $request, BaseModel $model, array &$data)
    {
        return $model->saveData($data);
    }

    protected function afterDetail(Request $request, &$data)
    {
        $userId = getUserId();
        $data['customer'] = $data['customer_id'] ? Customers::getOne($userId, $data['customer_id'], ['customer_id', 'company_name', 'company_short_name', 'contact_name']) : null;
        $data['business'] = $data['bo_id'] ? BusinessOpportunities::getOne($userId, $data['bo_id'], ['bo_id', 'content']) : null;
    }

    protected function afterList(Request $request, array &$data)
    {
        if ($data['list']) {
            $customerIds = $boIds = [];
            foreach ($data['list'] as &$item) {
                $customerIds[] = $item['customer_id'];
                $boIds[] = $item['bo_id'];
            }
            $customerList = array_column(Customers::select(['customer_id', 'company_name', 'company_short_name', 'contact_name'])->whereIn('customer_id', $customerIds)->get()->toArray(), null, 'customer_id');
            $businessList = array_column(BusinessOpportunities::select(['bo_id', 'content', 'title'])->whereIn('bo_id', $boIds)->get()->toArray(), null, 'bo_id');
            foreach ($data['list'] as &$item) {
                $item['customer'] = $customerList[$item['customer_id']] ?? null;
                $item['business'] = $businessList[$item['bo_id']] ?? null;
            }
        }
    }

    public function sort(Request $request)
    {
        $userId = getUserId();
        $data = v::input($request->all(), [
            'id' => v::notEmpty()->setName('id'),
            'sort_no' => v::notEmpty()->setName('sort_no'),
        ]);
        $model = $this->model->getOne($userId, $data['id'], ['id', 'sort_no']);
        if (empty($model)) {
            throw new BusinessException('待办事项不存在');
        }
        if ($model['sort_no'] == $data['sort_no']) {
            return $this->success();
        }
        $model->saveData($data);
        return $this->success();
    }
}