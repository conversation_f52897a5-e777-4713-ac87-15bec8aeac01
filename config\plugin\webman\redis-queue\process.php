<?php
return [
    'consumer_fast' => [
        'handler' => Webman\RedisQueue\Process\Consumer::class,
        'count' => 5, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis/fast'
        ]
    ],
    'consumer_slow' => [
        'handler' => Webman\RedisQueue\Process\Consumer::class,
        'count' => 8, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis/slow'
        ]
    ]
];