<?php

namespace app\api\controller;

use app\api\service\AttachmentService;
use app\controller\BaseController;
use app\model\BaseModel;
use app\model\BusinessOpportunities;
use app\model\Customers;
use app\model\TodoItems;
use app\model\User;
use library\Alioss;
use Respect\Validation\Validator as v;

use support\Request;
use Webman\Exception\BusinessException;

class UserController extends BaseController
{
    protected $noNeedLogin = ['login'];
    protected $ossUtil;

    public function __construct()
    {
        parent::__construct();
        $this->model = new User();
        $this->ossUtil = new Alioss;
    }

    public function login(Request $request)
    {
        $data = $request->post();
        v::input($data, [
            'third_id' => v::notEmpty()->setName('第三方id'),
            'auth' => v::notEmpty()->setName('授权码'),
        ]);
        if ($data['auth'] != env('API_KEY')) {
            throw new BusinessException('授权码错误');
        }
        $model = User::getByThirdId($data['third_id']);
        if (empty($model)) {
            $model = (new $this->model);
            $this->saveOpt($request, $model, $data);
        }
        $model->portrait = $model->portrait ? $this->ossUtil->genGetSign($model->portrait)['sign_url']: '';
        return $this->success(array_merge($model->toArray(), ['token' => User::genToken($model->id, $data['third_id'])]));
    }

    public function detail(Request $request)
    {
        $data = $this->model->find(getUserId());
        $data['portrait'] = $data['portrait'] ? $this->ossUtil->genGetSign($data['portrait'])['sign_url']: '';
        $data['desc'] = $data['profile'];
        unset($data['profile']);
        return $this->success($data);
    }

    public function config(Request $request)
    {
        return $this->success([
            'business_opportunities' => [
                'status_map' => BusinessOpportunities::STATUS_MAP,
            ],
            'upload' => [
                'chat_file_type' => AttachmentService::READ_TYPE,
            ],
        ]);
    }

    public function save(Request $request)
    {
        $data = $request->post();
        $uid = getUserId();
        $model = $this->model::query()->find($uid);
        isset($data['desc']) && $data['profile'] = $data['desc'];
        $model->fill($data)->save();
        return $this->success();
    }

}