<?php

namespace app\model;

use support\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InviteCode extends Model
{
    use SoftDeletes;

    protected $table = 'invite_code';
    protected $primaryKey = 'id';
    public $timestamps = true;

    public static function getByCode($code, $fields = ['*'])
    {
        return self::where('code', $code)
            ->first($fields);
    }

    public static function used($code, $userId)
    {
        return self::where("code", $code)
            ->update([
                'user_id' => $userId,
                'is_used' => 1
            ]);
    }


}