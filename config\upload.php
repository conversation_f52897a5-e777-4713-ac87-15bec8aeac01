<?php

return [
    'type' => 'alioss',
    // 最大上传
    'maxsize'  => 1024 * 1024 * 1000,
    // 文件格式限制
    // 'mimetype' => 'jpg,png,bmp,jpeg,gif,webp,zip,rar,xls,xlsx,doc,docx,wav,mp4,mp3,txt,exe,pdf,apk',
    'mimetype' => '*',
    'alioss' => [
        'accessKeyId' => env('ALIOSS_ACCESS_KEY_ID', ''),
        'accessKeySecret' => env('ALIOSS_ACCESS_KEY_SECRET', ''),
        'region' => env('ALIOSS_REGION', ''),
        'endpoint' => env('ALIOSS_ENDPOINT', ''),
        'bucket' => env('ALIOSS_BUCKET', ''),
        'cname' => env('ALIOSS_CNAME', ''),
    ]
];