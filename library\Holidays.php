<?php

namespace library;

use GuzzleHttp\Client;
use support\Log;

class Holidays
{

    /**
     * get 法定节假日信息
     * isOffDay: true表示是节假日， false表示是补班日
     */
    public static function getHolidays($year)
    {
        try {
            $client = new Client();
            $res = $client->request('GET', 'https://api.jiejiariapi.com/v1/holidays/' . $year, []);
            return $res->getBody()->getContents();
        } catch (\Exception $e) {
            Log::info('没有数据 err=' . $e->getMessage());
            return "";
        }
    }


}