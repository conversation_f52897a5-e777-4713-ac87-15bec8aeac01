APP_DEBUG = true
APP_ENV = dev

APP_SERVER_PORT = 8787

# api访问域名
API_DOMAIN =
# 前端访问域名
WEB_DOMAIN =

# 内部api访问秘钥
API_KEY =

# aes配置
AES_KEY =

# 数据库配置
DATABASE_HOST = 
DATABASE_PORT =
DATABASE_NAME =
DATABASE_USERNAME =
DATABASE_PASSWORD =

# redis配置
REDIS_HOST =
REDIS_PORT =
REDIS_PASSWORD =

# alioss
ALIOSS_ACCESS_KEY_ID =
ALIOSS_ACCESS_KEY_SECRET =
ALIOSS_REGION =
ALIOSS_ENDPOINT =
ALIOSS_BUCKET =
ALIOSS_CNAME =

# 企微机器人websocket 配置
WECHAT_WEBSOCKET_HOST ="127.0.0.1"
WECHAT_WEBSOCKET_PORT ="8788"

## ai agent 服务地址
AI_AGENT_SERVER = "127.0.0.1:8000"

APP_DOMAIN=http://apitest.profly.com.cn

#企微群告警机器人webhook地址
WECHAT_ERROR_WEBHOOK=""

#mysql 连接池配置
MYSQL_POOL_MAX_CONNECTIONS = 10
MYSQL_POOL_MIN_CONNECTIONS = 5