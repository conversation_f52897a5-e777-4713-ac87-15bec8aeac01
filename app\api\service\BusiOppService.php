<?php

namespace app\api\service;

use app\model\BusinessOpportunities;
use app\pyapi\service\BusiOppService as PyBusiOppService;
use app\pyapi\service\LinkService;
use app\wxwork\service\NoticeService;

class BusiOppService
{
    public static function pushNewStageAiSuggest($boId, $userId, $stage, $title)
    {
        $aiResult = PyBusiOppService::aiGetStageAndSuggest($boId, $userId);
        $finishedSuggestIds = [];
        if (!empty($aiResult)) {
            foreach ($aiResult['stage_goals'] ?? [] as $item) {
                !empty($item['goal_id']) && !empty($item['is_finished']) && $finishedSuggestIds[] = $item['goal_id'];
            }
            // ai建议写进消息表
            $newStageName = BusinessOpportunities::STATUS_MAP[$stage] ?? '';
            $content = "商机（ID为{$boId}，标题：{$title}）已进入新的阶段“{$newStageName}”。\n" . $aiResult['guidance_text'];
            NoticeService::pushNotice($userId, $content, addToChat: true, payload: ['url' => LinkService::genChat()]);
        }
        // 当前阶段之前的推荐待办全部改成完成
        $finishedSuggestIds = array_unique(array_merge($finishedSuggestIds, array_column(BusinessOpportunities::getAllBeforeTodoSuggest($stage), 'id')));
        $data['suggest_todo_finish_ids'] = implode(',', $finishedSuggestIds);
        BusinessOpportunities::getUserQuery($userId)->where('bo_id', $boId)->update($data);
    }
}