<?php

namespace library;

use DateTime;
use DateTimeZone;
use OSS\Credentials\StaticCredentialsProvider;
use OSS\Http\RequestCore;
use OSS\Http\ResponseCore;
use OSS\OssClient;

class Alioss
{
    protected $id;
    protected $key;
    protected $region;
    protected $endpoint;
    protected $bucket;
    protected $host;
    protected $cname;
    protected $client;
    
    public function __construct()
    {
        $this->id = config('upload.alioss.accessKeyId');
        $this->key = config('upload.alioss.accessKeySecret');
        $this->region = config('upload.alioss.region');
        $this->endpoint = config('upload.alioss.endpoint');
        $this->bucket = config('upload.alioss.bucket');
        $this->cname = config('upload.alioss.cname');
        $this->host = config('upload.alioss.cname') ? $this->cname : 'https://' . $this->bucket . '.' . $this->endpoint;
        $provider = new StaticCredentialsProvider($this->id, $this->key);
        $config = array(  
            "provider" => $provider,
            "endpoint" => $this->endpoint,
            'signatureVersion'=>OssClient::OSS_SIGNATURE_VERSION_V4,
            "region"=> $this->region
        );
        $this->client = new OssClient($config);
    }

    public function getClient()
    {
        return $this->client;
    }

    public function deleteObjects(array $objects)
    {
        return $this->client->deleteObjects($this->bucket, $objects);
    }

    /**
     * 生成web端oss上传签名
     */
    public function genPostSign($cid, $fileName, $contentType, $dir = '')
    {
        $expire = 3600; // 设置该policy超时时间，即这个policy过了这个有效时间，将不能访问
        // 手动生成签名
        $bucketUrl    = 'https://' . $this->bucket . '.' . $this->endpoint;
        $maxSize      = config('upload.maxsize');
        $conditions[] = ['content-length-range', 0, $maxSize];
        $policy       = base64_encode(json_encode([
            'expiration' => date('Y-m-d\TH:i:s.Z\Z', time() + $expire),
            'conditions' => $conditions,
        ]));
        $signature    = base64_encode(hash_hmac('sha1', $policy, $this->key, true));

        // sdk 生成签名链接
        if(empty($dir)) {
            $dir = env('APP_ENV', 'test') . "/" . $cid . "/" . date('Y') . '/' . date('m');
        }

        $object = "$dir/$fileName";
        $signedUrl = $this->client->signUrl($this->bucket, $object, $expire, "PUT", [
            OssClient::OSS_CONTENT_TYPE => $contentType,
        ]);

        // print(__FUNCTION__ . ": signedUrl: " . $signedUrl . "\n");
        // // 上传验证
        // $content = 'test';
        // $request = new RequestCore($signedUrl);
        // $request->set_method('PUT');
        // $request->add_header('Content-Type', '');
        // $request->add_header('Content-Length', strlen($content));
        // $request->set_body($content);
        // $request->send_request();
        // $res = new ResponseCore($request->get_response_header(),
        //     $request->get_response_body(), $request->get_response_code());
        // if ($res->isOK()) {
        //     print(__FUNCTION__ . ": OK" . "\n");
        // } else {
        //     print(__FUNCTION__ . ": FAILED" . "\n");
        // };

        return [
            'maxsize' => config('upload.maxsize'),
            'mimetype' => config('upload.mimetype'),
            'object' => $object,
            'sign_url' => str_replace('http://', 'https://', $signedUrl),
            'cdn'    => $this->cname ?: $bucketUrl,
            'url'    => $bucketUrl,
            'params' => [
                'OSSAccessKeyId' => $this->id,
                'policy'         => $policy,
                'Signature'      => $signature,
                'Expires'        => $expire
            ]
        ];

        
    }

    /**
     * 生成web端oss下载签名
     */
    public function genGetSign(string $object)
    {
        $timeout = 86400;
        $signedUrl = $this->client->signUrl($this->bucket, $object, $timeout);
        // print(__FUNCTION__ . ": signedUrl: " . $signedUrl . "\n");
        // /**
        //  * 访问验证
        //  */
        // $request = new RequestCore($signedUrl);
        // $request->set_method('GET');
        // $request->add_header('Content-Type', '');
        // $request->send_request();
        // $res = new ResponseCore($request->get_response_header(), $request->get_response_body(), $request->get_response_code());
        // if ($res->isOK()) {
        //     print(__FUNCTION__ . ": OK" . "\n");
        // } else {
        //     print(__FUNCTION__ . ": FAILED" . "\n");
        // };

        return [
            'sign_url' => str_replace('http://', 'https://', $signedUrl),
        ];
    }
}