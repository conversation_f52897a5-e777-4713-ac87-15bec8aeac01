<?php

namespace app\api\service;

use app\model\Attachment;
use app\model\MainAgentSessions;
use app\model\TaskResult;
use app\model\TodoItems;
use app\model\UserChat;
use app\queue\redis\fast\AppAiTaskQueue;
use app\queue\redis\slow\SlowFunctionCall;
use app\wechat\service\WebsocketService;
use app\wxwork\service\WxUserService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use library\ai\qwen;
use library\Alioss;
use library\redisKeys;
use library\tool;
use support\Db;
use support\Log;
use support\Redis;
use Webman\RedisQueue\Redis as RedisQueue;

class AiTaskService
{

    public static function createTask($userId, string|array $query, string $sessionId = '', $todoId = 0)
    {
        $taskId = tool::uuid();
        $sessionId = $sessionId ?: WxUserService::getSessionId($userId);
        $baseData = [
            'user_id' => $userId,
            'session_id' => $sessionId,
        ];
        $dataList = [];
        $readAttachmentIds = $otherAttachmentIds = $attachmentIds = [];
        if (is_array($query)) {
            $content = '';
            $attachments = "";
            foreach ($query as $k => $v) {
                // $v = array_merge(['content_type' => 'str', 'attachment_id' => 0], $v, $baseData);
                if (!empty($v['attachment_id'])) {
                    $v['attachment_name'] = $v['attachment_name'] ?? '';
                    $v['content_type'] != 'str' && $v['content_type'] = strtolower(pathinfo($v['content'], PATHINFO_EXTENSION)); // 自动获取文件类型
                    $attachments .= "fileName：" . $v['attachment_name'] . '  fileUrl： ' . AttachmentService::genViewUrlById($v['attachment_id'], $v['content_type']) . "\n";
                    $attachmentIds[] = $v['attachment_id'];
                    // 可读文档分类改为0等待归档, 其他类型文件分类改为其他
                    if (in_array($v['content_type'], AttachmentService::READ_TYPE)) {
                        $readAttachmentIds[] = $v['attachment_id'];
                    } else {
                        $otherAttachmentIds[] = $v['attachment_id'];
                    }
                } else {
                    $content .= $v['content'] . "\n";
                }
                unset($v['attachment_name']);
                $dataList[] = $v;
            }
            //拼接上传文件信息和文字内容
            if ($attachments) {
                $content = $content . "\n" . "[上传的文件] \n" . $attachments;
            }

            $dataList = [array_merge(['content' => json_encode($dataList, JSON_UNESCAPED_UNICODE), 'content_type' => 'mix'], $baseData)];
        } else {
            $content = $query;
            $dataList[] = array_merge($baseData, ['content' => $query]);
        }
        if (empty($todoId) && !UserChat::hasUserMsg($userId, $sessionId)) { // 首次发消息加上引导语作为上下文
            $content = [
                ['role' => 'user', 'content' => '你好'],
                ['role' => 'assistant', 'content' => UserChat::INTRODUCE],
                ['role' => 'user', 'content' => $content],
            ];
        }
        !$todoId && UserChat::addAll($dataList);
        \app\wxwork\service\AiTaskService::postTask($userId, $sessionId, $content, $taskId);
        $res = RedisQueue::send(AppAiTaskQueue::QUEUE_NAME, array_merge($baseData, [
                'task_id' => $taskId,
                'query' => $content,
                'task_expire_time' => time() + 600,
                'todo_id' => $todoId,
            ])
        );
        if (!empty($readAttachmentIds)) { // AI自动分类
            Attachment::whereIn('id', $readAttachmentIds)->update(['classify' => 0]);
            SlowFunctionCall::send('\app\api\service\AttachmentService', 'autoClassifyByAttachmentIds', [$userId, $readAttachmentIds], 180);
        }
        if (!empty($otherAttachmentIds)) {
            Attachment::whereIn('id', $otherAttachmentIds)->update(['classify' => Attachment::CLASSIFY_OTHER]);
        }
        return $res ? $taskId : false;
    }


    public static function isFinished($res)
    {
        return !empty($res['event']) && $res['event'] == 'RunCompleted';
    }

    public static function finished($userId, $sessionId, $taskId, $todoId = 0)
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        $list = Redis::lRange($key, 0, -1);
        $messages = [];
        $result = "";
        // 获取所有重复工具id
        $toolIds = $repeatToolIds = [];
        foreach ($list as $k => $v) {
            $v = json_decode($v, true);
            if (!empty($v['tool'])) {
                $toolId = $v['tool']['tool_call_id'];
                if (in_array($toolId, $toolIds) && !in_array($toolId, $repeatToolIds)) {
                    $repeatToolIds[] = $toolId;
                }
                $toolIds[] = $toolId;
            }
        }
        foreach ($list as $k => $v) {
            $v = json_decode($v, true);
            if (!empty($v['tool'])) {
                //过滤重复工具显示
                if (in_array($v['tool']['tool_call_id'], $repeatToolIds) && empty($v['tool']['tool_result'])) {
                    continue;
                }
                $messages[] = ['role' => 'tool', 'content' => $v['tool']];
            } elseif ($v['event'] == 'RunCompleted') {
                $messages[] = ['role' => 'assistant', 'content' => $v['content']];
                $result = $v['content'];
            }
        }
        Db::transaction(function () use ($userId, $sessionId, $taskId, $list, $messages, $todoId, $result) {
            $id = static::saveResult($userId, $taskId, $list);
            if ($todoId) {
                TodoItems::pushAiTodoNotice($userId, $todoId, $result, $id);
                MainAgentSessions::delBySessionId($userId, $sessionId);
            } else {
                UserChat::addOne([
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'task_id' => $id,
                    'content' => json_encode($messages, JSON_UNESCAPED_UNICODE),
                    'is_robot' => 1,
                ]);
            }
        });
    }

    //
    public static function saveResult($uid, $taskId, array $list = [])
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        empty($list) && $list = Redis::lRange($key, 0, -1);
        if (empty($list)) {
            return false;
        }
        $jsonStr = "[";
        foreach ($list as $k => $v) {
            if ($k == 0) {
                $jsonStr .= $v;
            } else {
                $jsonStr .= "," . $v;
            }
        }
        $jsonStr .= "]";
        return TaskResult::addOne([
            'user_id' => $uid,
            'task_id' => $taskId,
            'result' => $jsonStr,
        ]);
    }

    public static function getResults($userId, $taskId)
    {
        $key = redisKeys::AI_TASK_RESULT . $taskId;
        if (Redis::exists($key)) {
            //优先从缓存获取
            $list = Redis::lRange($key, 0, -1);
            if (empty($list)) {
                return [];
            }
            $jsonStr = "[";
            foreach ($list as $k => $v) {
                if ($k == 0) {
                    $jsonStr .= $v;
                } else {
                    $jsonStr .= "," . $v;
                }
            }
            $jsonStr .= "]";
            return json_decode($jsonStr, true) ?: [];
        }
        // 缓存过期，从db中获取
        $res = TaskResult::getOneByTaskId($userId, $taskId);
        return $res ? (json_decode($res['result'], true) ?: []) : [];
    }

}