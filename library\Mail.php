<?php

namespace library;

use AlibabaCloud\SDK\Dm\V20151123\Dm;
use AlibabaCloud\Credentials\Credential;
use AlibabaCloud\SDK\Dm\V20151123\Models\GetTrackListResponseBody\data\stat;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dm\V20151123\Models\SingleSendMailRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use support\Log;
use Webman\RedisQueue\Redis;

class Mail
{

    public static function createClient()
    {
        $config = new Config([
            'type' => 'access_key',
            'accessKeyId' => getenv('ALIOSS_ACCESS_KEY_ID'),
            'accessKeySecret' => getenv('ALIOSS_ACCESS_KEY_SECRET'),
        ]);
        // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/311677.html。
//        $credential = new Credential();
//        $config = new Config([
//            "credential" => $credential
//        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/Dm
        $config->endpoint = "dm.aliyuncs.com";
        return new Dm($config);
    }

    /**
     * 发送邮件
     * @param string $alias 发送者别名
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     */
    public static function send($alias, $subject, $content, $toAddress)
    {
        $client = self::createClient();
        $singleSendMailRequest = new SingleSendMailRequest([
            "fromAlias" => $alias,
            "toAddress" => $toAddress,
            "accountName" => "<EMAIL>",
            "addressType" => 1,
            "subject" => $subject,
            //"htmlBody" => $content,
            "textBody" => $content,
            "replyToAddress" => true
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            Log::error('发送邮件失败：' . $error->message);
            return false;
        }
    }

    //发送到消息队列，异步发送
    public static function sendQueue($subject, $content, $toAddress, $alias = 'profly')
    {
        Redis::send('send_mail', [
            'alias' => $alias,
            'subject' => $subject,
            'content' => $content,
            'toAddress' => $toAddress,
        ]);
    }

    /**
     * 错误信息告警 - 发送指定开发人员
     * @param string $subject 错误的标题(功能模块名)
     * @param string $content 错误的详细log
     **/
    public static function sendErrorNotice(string $subject, string $content)
    {
        $toAddress = ["<EMAIL>", "<EMAIL>"];
        foreach ($toAddress as $v) {
            Redis::send('send_mail', [
                'alias' => "错误告警",
                'subject' => $subject,
                'content' => $content,
                'toAddress' => $v,
            ]);
        }
    }
}