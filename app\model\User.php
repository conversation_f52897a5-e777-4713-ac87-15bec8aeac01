<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;
use library\tool;
use support\Model;
use Webman\Exception\BusinessException;

class User extends BaseModel
{
    use SoftDeletes;

    protected $table = 'user';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $fillable = ['third_id', 'name', 'mobile', 'portrait', 'company', 'business_type', 'profile', 'is_activated', 'cid'];

    public static function getByThirdId($thirdId, $fields = ['*'])
    {
        return self::select($fields)
            ->where('third_id', $thirdId)
            ->first();
    }

    public static function getById($userId, $fields = ['*'])
    {
        return self::select($fields)
            ->where('id', $userId)
            ->first();
    }

    public static function getByMobile($mobile, $fields = ['*'])
    {
        return self::select($fields)
            ->where('mobile', $mobile)
            ->first();
    }

    public static function genToken($uid)
    {
        $aesKey = env('AES_KEY', '');
        $iv = tool::randomString(16);
        $token = tool::aesEncrypt(json_encode(['id' => $uid, 'expire' => time() + config('session.lifetime'), 'env' => env('APP_ENV')]), $aesKey, $iv);
        return $token ? $iv . $token : false;
    }

    public static function parseToken($token)
    {
        $aesKey = env('AES_KEY', '');
        $iv = substr($token, 0, 16);
        $token = substr($token, 16);
        $decryptedData = tool::aesDecrypt($token, $aesKey, $iv);
        return $decryptedData ? json_decode($decryptedData, true) : false;
    }

    public static function loginByToken($token)
    {
        $data = self::parseToken($token);
        if ($data) {
            if (!empty($data['env']) && $data['env'] != env('APP_ENV')) {
                throw new BusinessException('当前环境未登录', 401);
            }
            if ($data['expire'] < time()) {
                throw new BusinessException('登录已失效', 401);
            }
        } else {
            throw new BusinessException('无效的token', 401);
        }
        $user = session('user');
        if (!$user || json_encode($data) != json_encode($user)) {
            $info = self::find($data['id'], ['id', 'third_id', 'mobile']);
            if (!$info) {
                throw new BusinessException('用户不存在', 401);
            }
            $user = array_merge($info->toArray(), $data);
            session()->set('user', $user);
        }
        request()->context['user'] = $user;
    }

    public static function edit($userId, $data)
    {
        return self::where('id', $userId)
            ->update($data);
    }

}