<?php

namespace app\process;

use app\api\service\AttachmentService;
use app\api\service\ChatService;
use app\api\service\CustomerService;
use App\model\Holidays;
use app\model\ShortLink;
use app\model\TodoItems;
use app\model\User;
use app\pyapi\service\BusiOppService;
use app\wxwork\service\NoticeService;
use Overtrue\ChineseCalendar\Calendar;
use support\Db;
use support\Log;
use Workerman\Crontab\Crontab;

class ScheduleTask
{
    public function onWorkerStart()
    {
        // 法定节假日祝福语放假前一天15:30推送
        new Crontab('30 15 * * *', function () {
            $date = date('Y-m-d', time() + 86400);
            $name = Holidays::isHolidayFirstDay($date);
            Log::info("{$date}的法定节日名称为: " . $name);
            if ($name) {
                $names = explode(',', $name);
                foreach ($names as $name) {
                    Holidays::pushHolidayGreetingToAllUser($name);
                }
            }
        });

        // 非法定节假日祝福语当天10:00推送
        $calendar = new Calendar();
        $dateMapHoliday = ['2025-12-25' => '圣诞节'];
        new Crontab('0 10 * * *', function () use ($calendar, $dateMapHoliday) {
            $date = date('Y-m-d');
            $name = $dateMapHoliday[$date] ?? '';
            if (empty($name)) {
                $dateArr = explode('-', $date);
                $result = $calendar->solar($dateArr[0], (int)$dateArr[1], (int)$dateArr[2]); // 阳历
                if ($result['lunar_month_chinese'] == '正月' && $result['lunar_day_chinese'] == '十五') {
                    $name = '元宵节';
                }
            }
            Log::info("{$date}的非法定节日名称为: " . $name);
            if ($name) {
                Holidays::pushHolidayGreetingToAllUser($name);
            }
        });

        new Crontab('0 9 * * *', function () {
            Log::info('开始执行客户商机智能跟进提醒');
            BusiOppService::followupRemind();
            Log::info('客户商机智能跟进提醒执行完毕');
        });

        new Crontab('0 10 * * *', function () {
            Log::info('开始执行客户联系人生日提醒');
            CustomerService::contactBirthdayRemind();
            Log::info('客户联系人生日提醒执行完毕');
        });

        //同步明年节假日数据 11-12月每天1:00执行一次
        new Crontab('0 5 * 11-12 *', function () {
            //Log::info('定时任务执行');
            $year = (int)date('Y') + 1;
            Holidays::syncHolidays($year);
        });

        new Crontab('0 3 * * *', function () {
            Log::info('开始执行临时文件和已删除文件清理');
            $service = new AttachmentService;
            $tmpCnt = $service::clearTempFile();
            $delFiles = $service->clearDeletedFile();
            Log::info('临时文件和已删除文件清理执行完毕: 清理临时文件' . $tmpCnt . '个，已删除文件:' . implode(',', $delFiles));
        });

        new Crontab('30 0 * * *', function () {
            $userIds = User::query()->pluck('id')->toArray();
            foreach ($userIds as $userId) {
                Log::info("开始执行客户聊天记录归类: user_id=$userId");
                ChatService::classifyUserChat($userId);
                Log::info("客户聊天记录归类执行完毕: user_id=$userId");
            }
        });

    }
}