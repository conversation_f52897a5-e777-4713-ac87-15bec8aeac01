<?php

namespace app\pyapi\controller;

use library\Mail;
use library\WxRobotNotice;
use Respect\Validation\Validator as v;
use support\Request;

class ErrorNoticeController extends BaseController
{
    public function send(Request $request)
    {
        $data = v::input($request->post(), [
            'subject' => v::notEmpty()->setName('标题'),
            'content' => v::notEmpty()->setName('内容'),
        ]);
        //Mail::sendErrorNotice($data['subject'], $data['content']);
        WxRobotNotice::sendError($data['subject'], $data['content']);
        return $this->success('发送成功');
    }
}